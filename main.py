# cta_mvp/main.py

import json
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.simple_backtest import run_backtest, calculate_performance_metrics
from utils.plot import plot_equity_curve_with_signals
from utils.plot_pnl import plot_trade_pnl
from execution.simulated_executor import simulate_execution

if __name__ == "__main__":
    # 从配置文件读取参数
    with open("config/strategy_config.json", encoding="utf-8") as f:
        config = json.load(f)

    # 通过策略工厂加载策略
    strategy = StrategyFactory.create(config)
    strategy.validate_config()

    symbol = config["symbol"]
    timeframe = config["timeframe"]
    limit = config.get("limit", 500)

    print(f"获取 {symbol} 的 {timeframe} 数据，共 {limit} 条...")
    df = fetch_ohlcv(symbol, timeframe, limit=limit)

    # 生成信号
    signals = strategy.generate_signals(df)

    # 执行回测
    print("\n执行回测...")
    df_result, trade_log = run_backtest(df, signals)

    # 计算并打印性能指标
    metrics = calculate_performance_metrics(trade_log)
    total_return = metrics["总收益率"] * 100
    print(f"回测结果: 总收益率 {total_return:.2f}%")
    print(f"交易次数: {metrics['交易次数']}, 胜率: {metrics['胜率']:.2%}")
    print(f"平均盈亏比: {metrics['平均盈亏比']:.2f}")

    # 模拟执行
    print("\n执行模拟交易...")
    simulate_execution(df_result)

    # 绘制图表
    plot_equity_curve_with_signals(df_result)
    plot_trade_pnl("trade_log.csv")
