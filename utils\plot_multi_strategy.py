# utils/plot_multi_strategy.py

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import numpy as np
from typing import Dict, List, Optional

matplotlib.rcParams['font.family'] = 'Microsoft YaHei'  # 支持中文标题

def plot_equity_curves(equity_curves: pd.DataFrame, title: str = "策略净值曲线比较"):
    """
    绘制多个策略的净值曲线比较图
    
    :param equity_curves: 包含多个策略净值曲线的DataFrame，每一列是一个策略的净值
    :param title: 图表标题
    """
    plt.figure(figsize=(16, 8))
    
    # 绘制基准线（1.0）
    plt.axhline(y=1.0, color='gray', linestyle='--', alpha=0.5)
    
    # 绘制每个策略的净值曲线
    for strategy_name in equity_curves.columns:
        plt.plot(equity_curves.index, equity_curves[strategy_name], label=strategy_name)
    
    plt.title(title)
    plt.xlabel("时间")
    plt.ylabel("净值")
    plt.grid(True, linestyle="--", alpha=0.5)
    plt.legend()
    plt.tight_layout()
    plt.show()
    
def plot_performance_metrics(performance_metrics: pd.DataFrame):
    """
    绘制多个策略的性能指标比较图
    
    :param performance_metrics: 包含多个策略性能指标的DataFrame
    """
    # 格式化性能指标
    formatted_metrics = performance_metrics.copy()
    formatted_metrics["总收益率"] = formatted_metrics["总收益率"].apply(lambda x: f"{x:.2%}")
    formatted_metrics["年化收益率"] = formatted_metrics["年化收益率"].apply(lambda x: f"{x:.2%}")
    formatted_metrics["最大回撤"] = formatted_metrics["最大回撤"].apply(lambda x: f"{x:.2%}")
    formatted_metrics["夏普比率"] = formatted_metrics["夏普比率"].apply(lambda x: f"{x:.2f}")
    formatted_metrics["胜率"] = formatted_metrics["胜率"].apply(lambda x: f"{x:.2%}")
    formatted_metrics["盈亏比"] = formatted_metrics["盈亏比"].apply(lambda x: f"{x:.2f}")
    
    # 创建表格
    fig, ax = plt.subplots(figsize=(12, len(performance_metrics) * 0.8 + 1))
    ax.axis('off')
    
    # 创建表格
    table = ax.table(
        cellText=formatted_metrics.values,
        rowLabels=formatted_metrics.index,
        colLabels=formatted_metrics.columns,
        cellLoc='center',
        loc='center',
        bbox=[0, 0, 1, 1]
    )
    
    # 设置表格样式
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 1.5)
    
    # 设置标题
    plt.title("策略性能指标比较", pad=20)
    plt.tight_layout()
    plt.show()
    
def plot_drawdown_curves(results: Dict[str, pd.DataFrame], title: str = "策略回撤曲线比较"):
    """
    绘制多个策略的回撤曲线比较图
    
    :param results: 包含多个策略回测结果的字典，键为策略名称，值为回测结果DataFrame
    :param title: 图表标题
    """
    plt.figure(figsize=(16, 8))
    
    # 计算并绘制每个策略的回撤曲线
    for strategy_name, result in results.items():
        equity = result["equity"]
        cumulative_max = equity.cummax()
        drawdown = (equity - cumulative_max) / cumulative_max
        plt.plot(result.index, drawdown, label=f"{strategy_name} 回撤")
    
    plt.title(title)
    plt.xlabel("时间")
    plt.ylabel("回撤")
    plt.grid(True, linestyle="--", alpha=0.5)
    plt.legend()
    plt.tight_layout()
    plt.show()
    
def plot_monthly_returns_heatmap(results: Dict[str, pd.DataFrame], strategy_name: str):
    """
    绘制单个策略的月度收益热力图
    
    :param results: 包含多个策略回测结果的字典，键为策略名称，值为回测结果DataFrame
    :param strategy_name: 要绘制热力图的策略名称
    """
    if strategy_name not in results:
        print(f"策略 {strategy_name} 不存在")
        return
        
    result = results[strategy_name]
    
    # 计算每日收益率
    daily_returns = result["strategy_return"]
    
    # 转换为月度收益率
    monthly_returns = daily_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
    monthly_returns_table = pd.DataFrame(monthly_returns)
    monthly_returns_table.index = monthly_returns_table.index.strftime('%Y-%m')
    
    # 将月度收益率转换为年月格式的表格
    monthly_returns_pivot = pd.DataFrame()
    for date, ret in monthly_returns.items():
        year = date.year
        month = date.month
        if year not in monthly_returns_pivot.columns:
            monthly_returns_pivot[year] = [np.nan] * 12
        monthly_returns_pivot.loc[month, year] = ret
    
    monthly_returns_pivot.index = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    
    # 绘制热力图
    plt.figure(figsize=(12, 8))
    ax = plt.gca()
    
    # 创建热力图
    cmap = plt.cm.RdYlGn  # 红黄绿色图，负收益为红色，正收益为绿色
    im = ax.imshow(monthly_returns_pivot.values, cmap=cmap)
    
    # 设置坐标轴
    ax.set_xticks(np.arange(len(monthly_returns_pivot.columns)))
    ax.set_yticks(np.arange(len(monthly_returns_pivot.index)))
    ax.set_xticklabels(monthly_returns_pivot.columns)
    ax.set_yticklabels(monthly_returns_pivot.index)
    
    # 添加文本标注
    for i in range(len(monthly_returns_pivot.index)):
        for j in range(len(monthly_returns_pivot.columns)):
            value = monthly_returns_pivot.iloc[i, j]
            if not np.isnan(value):
                text = ax.text(j, i, f"{value:.2%}",
                               ha="center", va="center", 
                               color="black" if abs(value) < 0.1 else "white")
    
    plt.title(f"{strategy_name} 月度收益热力图")
    plt.colorbar(im, label="月度收益率")
    plt.tight_layout()
    plt.show()
