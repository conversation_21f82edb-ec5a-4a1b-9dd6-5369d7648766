# test_risk_debug.py - 简化的风控测试脚本

import json
import pandas as pd
import numpy as np
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.risk_managed_backtest import run_backtest_with_risk_management
import logging

# 设置日志级别
logging.basicConfig(
    level=logging.DEBUG,  # 临时启用DEBUG
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

if __name__ == "__main__":
    # 从配置文件读取参数
    with open("config/strategy_config.json", encoding="utf-8") as f:
        strategy_config = json.load(f)

    with open("config/risk_config.json", encoding="utf-8") as f:
        risk_config = json.load(f)

    print("风控配置:")
    print(f"  止损: {risk_config['stop_loss_pct']:.4f} ({risk_config['stop_loss_pct']*100:.2f}%)")
    print(f"  止盈: {risk_config['take_profit_pct']:.4f} ({risk_config['take_profit_pct']*100:.2f}%)")
    print(f"  跟踪止损: {risk_config['trailing_stop_pct']:.4f} ({risk_config['trailing_stop_pct']*100:.2f}%)")
    print(f"  单笔最大亏损: {risk_config['max_loss_per_trade']} USDT")

    # 通过策略工厂加载策略
    strategy = StrategyFactory.create(strategy_config)
    strategy.validate_config()

    symbol = strategy_config["symbol"]
    timeframe = strategy_config["timeframe"]
    limit = strategy_config.get("limit", 100)  # 减少数据量

    print(f"\n获取 {symbol} 的 {timeframe} 数据，共 {limit} 条...")
    df = fetch_ohlcv(symbol, timeframe, limit=limit)

    # 生成信号
    signals = strategy.generate_signals(df)

    # 确保signals包含position列（信号的差分）
    if "position" not in signals.columns:
        signals["position"] = signals["signal"].diff().fillna(0)

    print(f"\n策略信号统计:")
    print(f"  买入信号数量: {(signals['position'] == 1).sum()}")
    print(f"  卖出信号数量: {(signals['position'] == -1).sum()}")

    # 显示所有信号
    signal_changes = signals[signals['position'] != 0]
    print(f"\n所有信号变化:")
    for time, row in signal_changes.iterrows():
        print(f"  {time}: position={row['position']}, signal={row['signal']}")

    # 分析开仓后的价格变化
    buy_signals = signals[signals['position'] == 1]
    if not buy_signals.empty:
        first_buy_time = buy_signals.index[0]
        first_buy_price = df.loc[first_buy_time, 'close']
        print(f"\n第一个买入信号:")
        print(f"  时间: {first_buy_time}")
        print(f"  价格: {first_buy_price:.2f}")

        # 查看开仓后的价格变化
        after_buy = df.loc[first_buy_time:]
        print(f"\n开仓后价格变化 (前10个):")
        for i, (time, row) in enumerate(after_buy.head(10).iterrows()):
            price = row['close']
            pnl_pct = (price - first_buy_price) / first_buy_price
            print(f"  {i}: {time} 价格={price:.2f}, 盈亏={pnl_pct:.4f} ({pnl_pct*100:.2f}%)")

        # 查找最大跌幅和涨幅
        all_pnl = (after_buy['close'] - first_buy_price) / first_buy_price
        min_pnl = all_pnl.min()
        max_pnl = all_pnl.max()
        print(f"\n开仓后统计:")
        print(f"  最大跌幅: {min_pnl:.4f} ({min_pnl*100:.2f}%)")
        print(f"  最大涨幅: {max_pnl:.4f} ({max_pnl*100:.2f}%)")
        print(f"  止损阈值: -{risk_config['stop_loss_pct']:.4f} (-{risk_config['stop_loss_pct']*100:.2f}%)")
        print(f"  止盈阈值: {risk_config['take_profit_pct']:.4f} ({risk_config['take_profit_pct']*100:.2f}%)")

        # 检查是否应该触发风控
        if min_pnl <= -risk_config['stop_loss_pct']:
            print(f"  ✅ 应该触发止损!")
        if max_pnl >= risk_config['take_profit_pct']:
            print(f"  ✅ 应该触发止盈!")
        if min_pnl > -risk_config['stop_loss_pct'] and max_pnl < risk_config['take_profit_pct']:
            print(f"  ❌ 未达到风控触发条件")

    # 带风控的回测
    print("\n执行带风控的回测...")
    df_result, trade_log = run_backtest_with_risk_management(df, signals, risk_config)

    # 打印回测结果
    total_return = (df_result["equity"].iloc[-1] - 1) * 100
    print(f"\n回测结果: 总收益率 {total_return:.2f}%")

    # 计算胜率
    if trade_log:
        win_trades = [t for t in trade_log if t["pnl"] > 0]
        win_rate = len(win_trades) / len(trade_log) * 100
        print(f"交易次数: {len(trade_log)}, 胜率: {win_rate:.2f}%")

        print("\n交易详情:")
        for i, trade in enumerate(trade_log):
            print(f"  交易{i+1}: 入场={trade['entry_price']:.2f}, 出场={trade['exit_price']:.2f}, "
                  f"盈亏={trade['pnl']:.2f}, 原因={trade['exit_reason']}")
    else:
        print("没有完成的交易")

    # 检查最后的持仓状态
    final_position = df_result["actual_position"].iloc[-1]
    if final_position != 0:
        print(f"\n⚠️ 最终持仓状态: {final_position}")
