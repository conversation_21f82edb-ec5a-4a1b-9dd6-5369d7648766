# backtest/risk_managed_backtest.py

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from risk_management.risk_manager import ComprehensiveRiskManager
import logging

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def run_backtest_with_risk_management(df: pd.DataFrame, signals: pd.DataFrame,
                                     risk_config: Dict = None,
                                     slippage: float = 0.0005,
                                     fee: float = 0.001) -> Tuple[pd.DataFrame, List[Dict]]:
    """
    带风控的回测函数

    :param df: 原始行情数据
    :param signals: 策略信号数据
    :param risk_config: 风控配置
    :param slippage: 滑点
    :param fee: 手续费
    :return: 回测结果DataFrame和交易记录列表
    """
    # 默认风控配置
    if risk_config is None:
        risk_config = {
            "initial_capital": 10000.0,
            "position_ratio": 1.0,
            "max_positions": 1,
            "stop_loss_pct": 0.05,
            "take_profit_pct": 0.1,
            "trailing_stop_pct": 0.03,
            "max_drawdown_pct": 0.2,
            "position_sizing_method": "fixed_ratio"
        }

    # 创建风控管理器
    risk_manager = ComprehensiveRiskManager(risk_config)

    # 准备结果DataFrame
    result = df.copy()
    result["signal"] = signals["signal"]
    # 使用与策略相同的方式计算position（signal的差分）
    result["position"] = signals["position"]  # 直接使用策略生成的position
    result["price_change"] = result["close"].pct_change().fillna(0)

    # 初始化变量
    position = 0
    entry_price = 0
    entry_time = None
    trade_log = []

    # 检查策略类型
    has_negative_position = (result["position"] < 0).any()

    # 记录初始资金
    initial_capital = risk_manager.initial_capital
    current_capital = initial_capital

    # 创建资金曲线
    equity_curve = pd.Series(index=result.index, data=initial_capital)

    # 创建持仓列
    result["actual_position"] = 0

    # 遍历每个时间点
    for i, (time, row) in enumerate(result.iterrows()):
        price = row["close"]
        current_position = row["position"]

        # 检查风控触发的出场信号
        if position == 1:
            can_exit, exit_reason = risk_manager.check_exit_signal(result.iloc[:i+1], time, price, current_position, "default")
            if can_exit:
                exit_price = price
                exit_time = time
                position_size = risk_manager.get_position("default")["size"]
                pnl_pct = (exit_price - entry_price) / entry_price
                pnl = position_size * pnl_pct
                current_capital += pnl
                trade_log.append({
                    "entry_time": entry_time,
                    "entry_price": entry_price,
                    "exit_time": exit_time,
                    "exit_price": exit_price,
                    "pnl": pnl,
                    "exit_reason": exit_reason,
                    "capital": current_capital
                })
                logging.warning(f"[{exit_time}] ⚠️ 风控触发平仓: 价格={exit_price:.2f}, 盈亏={pnl:.2f}, 资金={current_capital:.2f}, 仓位={position_size:.2f}, 原因={exit_reason}")
                risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")
                position = 0

        # 处理策略信号 - 只在信号变化时触发买入（position=1表示信号从0变为1）
        elif row["position"] == 1 and position == 0:
            if risk_manager.check_entry_signal(result.iloc[:i+1], time, price, current_position, "default"):
                position_ratio = risk_manager.calculate_position_size(result.iloc[:i+1], time, price, "default")
                position_size = current_capital * position_ratio
                entry_price = price
                entry_time = time
                position = 1
                risk_manager.add_position("default", position_size, entry_price, entry_time)
                logging.info(f"[{time}] 🟢 开仓: 价格={price:.2f}, 资金={current_capital:.2f}, 仓位={position_size:.2f}, 信号={current_position}")

        # 处理策略卖出信号
        elif (has_negative_position and current_position == -1 and position == 1) or \
             (not has_negative_position and current_position == 0 and position == 1):
            exit_price = price
            exit_time = time
            position_size = risk_manager.get_position("default")["size"]
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct
            current_capital += pnl
            exit_reason = "strategy_signal"
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "exit_reason": exit_reason,
                "capital": current_capital
            })
            logging.info(f"[{exit_time}] 🔴 平仓: 价格={exit_price:.2f}, 盈亏={pnl:.2f}, 资金={current_capital:.2f}, 仓位={position_size:.2f}, 原因={exit_reason}")
            risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")
            position = 0

        # 计算当前总资金（包含未实现盈亏）
        if position == 1:
            # 有持仓时，计算未实现盈亏
            position_info = risk_manager.get_position("default")
            position_size = position_info["size"]
            entry_price = position_info["entry_price"]
            unrealized_pnl = position_size * (price - entry_price) / entry_price
            total_capital = current_capital + unrealized_pnl
        else:
            # 无持仓时，总资金就是已实现资金
            total_capital = current_capital

        equity_curve[time] = total_capital
        result.loc[time, "actual_position"] = position

    # 添加资金曲线到结果
    result["equity"] = equity_curve / initial_capital
    result["equity_curve"] = result["equity"]  # 兼容旧版本

    # 计算收益率
    result["strategy_return"] = result["equity"].pct_change().fillna(0)

    # 计算交易信号
    result["trade"] = result["actual_position"].diff().fillna(0)

    return result, trade_log
