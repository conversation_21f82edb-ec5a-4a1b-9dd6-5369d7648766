# CTA策略交易系统文档

## 系统概述
构建一个完整的自动化交易框架，涵盖从策略设计、回测、执行到风控、数据管理、可视化及监控的全流程。

## 核心模块架构

### 1. 策略模块
**功能**：
- 实现多种技术指标（如双均线、布林带突破等）
- 支持多组参数配置组合
- 策略封装为类结构提高可扩展性与维护性

**关键类**：
- [StrategyFactory](file://c:\project\CTA\cta_mvp\strategy\base.py#L9-L20)：策略工厂类
- [BaseStrategy](file://c:\project\CTA\cta_mvp\strategy\base.py#L2-L7)：策略基类
- [DualMAStrategy](file://c:\project\CTA\cta_mvp\strategy\dual_ma.py#L5-L30)：双均线策略实现
- [BollBreakoutStrategy](file://c:\project\CTA\cta_mvp\strategy\boll_breakout.py#L3-L26)：布林带突破策略实现

```mermaid
graph TD
    A[策略工厂] --> B{策略类型}
    B -->|双均线| C[DualMAStrategy]
    B -->|布林带突破| D[BollBreakoutStrategy]
    C --> E[计算短期/长期均线]
    D --> F[计算布林带指标]
    E --> G[生成买卖信号]
```

### 2. 回测模块
**功能**：
- 增加滑点和手续费模拟
- 支持多个策略之间的对比测试
- 核心功能包括资金管理、滑点模拟、手续费计算和绩效统计

**关键函数**：
- [run_backtest_with_risk_management](file://c:\project\CTA\cta_mvp\backtest\risk_managed_backtest.py#L7-L163)：核心回测函数

### 3. 执行模块
**功能**：
- 设计统一的"下单接口"类
- 兼容模拟盘和实盘交易所接入

**关键实现**：
- [simulate_execution_with_risk_management](file://c:\project\CTA\cta_mvp\execution\risk_managed_executor.py#L8-L166)：模拟交易执行器

### 4. 风控模块
**功能**：
- 实现止损止盈逻辑
- 支持最大亏损限制、仓位管理
- 综合多种风控策略

**关键类**：
- [RiskManager](file://c:\project\CTA\cta_mvp\risk_management\base.py#L6-L167)
- [ComprehensiveRiskManager](file://c:\project\CTA\cta_mvp\risk_management\risk_manager.py#L9-L114)
- [StopLossManager](file://c:\project\CTA\cta_mvp\risk_management\stop_loss.py#L7-L177)
- [PositionSizeManager](file://c:\project\CTA\cta_mvp\risk_management\position_sizing.py#L7-L160)

### 5. 数据模块
**功能**：
- 从Binance获取历史K线数据
- 支持多种时间周期
- 自动补全缺失数据

**关键实现**：
- [fetch_ohlcv](file://c:\project\CTA\cta_mvp\data\fetch_binance_kline.py#L6-L22)

### 6. 可视化模块
**功能**：
- 绘制资金曲线
- 展示交易信号
- 可视化每笔交易的盈亏

**关键实现**：
- [plot_equity_curve_with_signals](file://c:\project\CTA\cta_mvp\utils\plot.py#L5-L55)
- [plot_trade_pnl](file://c:\project\CTA\cta_mvp\utils\plot_pnl.py#L8-L23)

### 7. 配置管理
- `config/strategy_config.json`：主策略配置
- `config/risk_config.json`：风控配置
- `config/strategies/`目录下的具体策略配置文件

## 系统工作流程

### 主流程
```mermaid
graph TD
    A[初始化配置] --> B[创建策略]
    B --> C[获取数据]
    C --> D[生成信号]
    D --> E[回测验证]
    E --> F[模拟执行]
    F --> G[结果可视化]
```

### 风控处理流程
1. **入场检查**：验证是否符合所有风控规则
2. **仓位计算**：根据当前市场状况和资金规模计算仓位
3. **持仓监控**：持续跟踪持仓状态和盈亏变化
4. **出场判断**：触发止损、止盈或策略信号时平仓
5. **资金更新**：完成交易后更新账户资金

### 策略执行流程
1. **信号检测**：监测策略生成的交易信号变化
2. **风控验证**：确保交易符合所有风控规则
3. **订单执行**：按指定价格和数量执行交易
4. **记录更新**：更新资金、持仓和交易日志

## 关键业务逻辑说明

### 交易信号生成
以双均线策略为例：
- 当短期均线上穿长期均线时生成买入信号（signal=1）
- 当短期均线下穿长期均线时生成卖出信号（signal=-1）

### 风险管理逻辑
1. **止损机制**：实时监控持仓盈亏比例，当亏损达到预设阈值时强制平仓
2. **仓位控制**：根据资金规模和风险偏好动态调整每次交易的资金比例
3. **最大回撤限制**：监控账户整体风险，当亏损达到预设的最大回撤比例时停止交易
4. **最小持仓时间**：确保每次交易至少持有一定时间周期

### 回测引擎实现
1. **资金管理**：跟踪账户资金变化
2. **滑点模拟**：在成交价格上增加随机偏差
3. **手续费计算**：扣除预设比例的手续费
4. **绩效统计**：计算总收益率、胜率、平均盈亏比

### 交易执行细节
1. **信号匹配**：精确识别买卖信号时间点
2. **价格确认**：使用下一个K线的开盘价作为成交价
3. **资金计算**：根据仓位比例确定交易金额
4. **日志记录**：详细记录交易信息