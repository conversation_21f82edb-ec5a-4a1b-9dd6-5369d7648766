# strategy/dual_ma.py

import pandas as pd
from strategy.base import BaseStrategy, StrategyConfigError

class DualMAStrategy(BaseStrategy):
    """
    双均线策略实现类。
    
    参数：
        config (dict): 策略配置，需包含'short_window'和'long_window'。
    属性：
        short_window (int): 短期均线窗口
        long_window (int): 长期均线窗口
    """
    def __init__(self, config):
        """
        初始化双均线策略，自动校验参数。
        参数:
            config (dict): 策略配置，需包含'short_window'和'long_window'。
        """
        super().__init__(config)
        self.short_window = config["short_window"]
        self.long_window = config["long_window"]
        self.validate_config()  # 自动校验参数

    def validate_config(self):
        """
        校验策略参数的有效性。
        short_window和long_window必须为整数，且short_window < long_window。
        """
        if not isinstance(self.short_window, int) or not isinstance(self.long_window, int):
            raise StrategyConfigError("short_window 和 long_window 必须是整数")
        if self.short_window >= self.long_window:
            raise StrategyConfigError("short_window 应小于 long_window")

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        根据收盘价计算短期和长期均线，生成买卖信号。
        买入信号: 短期均线上穿长期均线。
        卖出信号: 短期均线下穿长期均线。
        
        参数:
            df (pd.DataFrame): 包含'close'列的行情数据
        返回:
            pd.DataFrame: 新增'short_ma', 'long_ma', 'signal', 'position'列
        """
        df = df.copy()
        df["short_ma"] = df["close"].rolling(window=self.short_window).mean()
        df["long_ma"] = df["close"].rolling(window=self.long_window).mean()
        df["signal"] = 0

        # 修复警告：使用iloc而不是loc进行位置索引
        start_idx = self.long_window
        if start_idx < len(df):
            mask = df["short_ma"].iloc[start_idx:] > df["long_ma"].iloc[start_idx:]
            df.loc[df.index[start_idx:], "signal"] = mask.astype(int)

        df["position"] = df["signal"].diff()
        return df
