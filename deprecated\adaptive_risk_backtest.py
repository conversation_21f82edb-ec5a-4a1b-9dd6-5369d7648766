# backtest/adaptive_risk_backtest.py

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from risk_management.adaptive_risk_manager import AdaptiveRisk<PERSON>ana<PERSON>

def run_backtest_with_adaptive_risk(df: pd.DataFrame, signals: pd.DataFrame,
                                  risk_config: Dict = None,
                                  slippage: float = 0.0005,
                                  fee: float = 0.001) -> <PERSON><PERSON>[pd.DataFrame, List[Dict]]:
    """
    带自适应风控的回测函数

    :param df: 原始行情数据
    :param signals: 策略信号数据
    :param risk_config: 风控配置
    :param slippage: 滑点
    :param fee: 手续费
    :return: 回测结果DataFrame和交易记录列表
    """
    # 默认风控配置
    if risk_config is None:
        risk_config = {
            "initial_capital": 10000.0,
            "position_ratio": 1.0,
            "max_positions": 1,
            "stop_loss_pct": 0.1,
            "take_profit_pct": 0.2,
            "trailing_stop_pct": 0.05,
            "trend_window": 20,
            "min_holding_periods": 10,
            "enable_trend_following": True
        }

    # 创建风控管理器
    risk_manager = AdaptiveRiskManager(risk_config)

    # 准备结果DataFrame
    result = df.copy()
    result["signal"] = signals["signal"]
    result["position"] = signals["signal"].shift(1).fillna(0)
    result["price_change"] = result["close"].pct_change().fillna(0)

    # 初始化变量
    position = 0
    entry_price = 0
    entry_time = None
    trade_log = []

    # 检查策略类型
    has_negative_position = (result["position"] < 0).any()

    # 记录初始资金
    initial_capital = risk_manager.initial_capital
    current_capital = initial_capital

    # 创建资金曲线
    equity_curve = pd.Series(index=result.index, data=initial_capital)

    # 创建持仓列
    result["actual_position"] = 0

    # 遍历每个时间点
    for i, (time, row) in enumerate(result.iterrows()):
        price = row["close"]
        current_position = row["position"]

        # 检查风控触发的出场信号
        if position == 1 and risk_manager.check_exit_signal(result.iloc[:i+1], time, price, current_position, "default"):
            # 风控触发的平仓
            exit_price = price
            exit_time = time

            # 计算盈亏
            position_size = risk_manager.get_position("default")["size"]
            # 计算盈亏百分比，然后乘以仓位大小
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            # 记录交易
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "exit_reason": "risk_management",
                "capital": current_capital
            })

            # 通知风控管理器
            risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")

            # 打印调试信息
            print(f"回测中记录交易: entry_time={entry_time}, exit_time={exit_time}, pnl={pnl}, trade_history={risk_manager.trade_history}")

            # 更新状态
            position = 0

        # 处理策略信号
        elif current_position == 1 and position == 0:
            # 检查风控是否允许入场
            if risk_manager.check_entry_signal(result.iloc[:i+1], time, price, current_position, "default"):
                # 计算仓位大小
                position_ratio = risk_config.get("position_ratio", 1.0)
                position_size = current_capital * position_ratio

                # 开仓
                entry_price = price
                entry_time = time
                position = 1

                # 记录持仓
                risk_manager.add_position("default", position_size, entry_price, entry_time)

        # 处理策略卖出信号
        elif (has_negative_position and current_position == -1 and position == 1) or \
             (not has_negative_position and current_position == 0 and position == 1):
            # 平仓
            exit_price = price
            exit_time = time

            # 计算盈亏
            position_size = risk_manager.get_position("default")["size"]
            # 计算盈亏百分比，然后乘以仓位大小
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            # 记录交易
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "exit_reason": "strategy_signal",
                "capital": current_capital
            })

            # 通知风控管理器
            risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")

            # 更新状态
            position = 0

        # 更新资金曲线和持仓状态
        equity_curve[time] = current_capital
        result.loc[time, "actual_position"] = position

    # 添加资金曲线到结果
    result["equity"] = equity_curve / initial_capital
    result["equity_curve"] = result["equity"]  # 兼容旧版本

    # 计算收益率
    result["strategy_return"] = result["equity"].pct_change().fillna(0)

    # 计算交易信号
    result["trade"] = result["actual_position"].diff().fillna(0)

    return result, trade_log
