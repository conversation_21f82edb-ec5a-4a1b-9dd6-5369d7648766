# CTA策略交易系统 - 可视化模块

## 📋 功能概述

本可视化模块使用 Streamlit 构建了一个完整的 Web 界面，提供以下核心功能：

### 🎯 主要功能
1. **📊 策略回测** - 单策略回测分析，包含K线图、信号标记、净值曲线等
2. **📈 多策略比较** - 多个策略的性能对比分析和排名
3. **🛡️ 风控回测** - 带风险管理的回测分析（开发中）
4. **📋 交易记录** - 详细的交易历史记录查看（开发中）
5. **⚙️ 参数配置** - 动态调整策略和回测参数（开发中）

### 🎨 可视化特性
- **交互式K线图** - 使用 Plotly 实现的可缩放、可交互的K线图
- **策略信号标记** - 清晰标记买入卖出信号点
- **净值曲线对比** - 多策略净值曲线同时展示
- **性能指标卡片** - 直观的指标展示卡片
- **回撤分析图** - 策略回撤曲线对比
- **综合排名** - 基于多维度指标的策略排名

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements_streamlit.txt
```

### 2. 启动应用
```bash
# 方式1：直接运行
streamlit run streamlit_app.py

# 方式2：使用批处理文件（Windows）
run_streamlit.bat
```

### 3. 访问界面
打开浏览器访问：http://localhost:8501

## 📖 使用指南

### 单策略回测
1. 在左侧面板选择策略配置文件
2. 调整回测参数（数据条数、初始资金、手续费等）
3. 点击"🚀 执行回测"按钮
4. 在右侧查看回测结果：
   - 📈 K线与信号：K线图和买卖信号标记
   - 💰 净值曲线：策略净值变化曲线
   - 📊 交易记录：详细交易记录和盈亏分布
   - 📋 详细指标：完整的性能指标

### 多策略比较
1. 选择要比较的多个策略
2. 设置统一的回测参数
3. 点击"🚀 执行多策略比较"
4. 查看比较结果：
   - 📈 净值曲线对比：所有策略净值曲线
   - 📊 性能指标对比：指标表格和柱状图
   - 📉 回撤分析：回撤曲线对比
   - 🏆 策略排名：综合评分排名

## 🎛️ 参数说明

### 回测参数
- **数据条数**：获取的历史数据K线数量
- **初始资金**：回测起始资金（USDT）
- **手续费率**：交易手续费比例
- **滑点**：模拟实际交易的价格滑点

### 性能指标
- **总收益率**：整个回测期间的总收益
- **年化收益率**：年化后的收益率
- **最大回撤**：最大资金回撤比例
- **夏普比率**：风险调整后收益指标
- **胜率**：盈利交易占总交易的比例
- **平均盈亏比**：平均盈利与平均亏损的比值

## 🔧 技术架构

### 前端框架
- **Streamlit**：Web应用框架
- **Plotly**：交互式图表库
- **Pandas**：数据处理

### 后端集成
- **策略模块**：strategy/
- **回测模块**：backtest/
- **数据模块**：data/
- **风控模块**：risk_management/

### 文件结构
```
├── streamlit_app.py          # 主应用文件
├── requirements_streamlit.txt # 依赖包列表
├── run_streamlit.bat         # 启动脚本
└── README_可视化模块.md      # 使用说明
```

## 🎯 开发计划

### 已完成功能 ✅
- [x] 基础应用框架
- [x] 单策略回测界面
- [x] 多策略比较功能
- [x] 交互式图表展示
- [x] 性能指标可视化
- [x] 策略综合排名

### 开发中功能 🚧
- [ ] 风控回测界面
- [ ] 交易记录管理
- [ ] 参数配置界面
- [ ] 实时监控功能
- [ ] 策略参数优化

### 计划功能 📋
- [ ] 用户认证系统
- [ ] 策略分享功能
- [ ] 报告导出功能
- [ ] 移动端适配
- [ ] 数据缓存优化

## 🐛 故障排除

### 常见问题
1. **导入错误**：确保项目根目录在Python路径中
2. **数据获取失败**：检查网络连接和API限制
3. **图表显示异常**：清除浏览器缓存重新加载
4. **性能较慢**：减少数据条数或优化策略代码

### 调试建议
- 查看终端输出的错误信息
- 检查策略配置文件格式
- 确认所有依赖包已正确安装

## 📞 技术支持

如有问题或建议，请：
1. 查看错误日志
2. 检查配置文件
3. 参考项目文档
4. 提交Issue反馈

---

**注意**：本可视化模块仍在持续开发中，部分功能可能不够完善。建议在测试环境中使用，生产环境请谨慎部署。
