# backtest/multi_strategy_backtest.py

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from strategy.base import BaseStrategy
from backtest.simple_backtest import run_backtest

class MultiStrategyBacktest:
    """
    多策略回测类，支持同时回测多个策略并进行比较
    """
    def __init__(self, df: pd.DataFrame, slippage: float = 0.0005, fee: float = 0.001):
        """
        初始化多策略回测类

        :param df: 包含OHLCV数据的DataFrame
        :param slippage: 滑点，默认为0.0005（0.05%）
        :param fee: 交易手续费，默认为0.001（0.1%）
        """
        self.df = df.copy()
        self.slippage = slippage
        self.fee = fee
        self.results = {}  # 存储各个策略的回测结果
        self.performance_metrics = {}  # 存储各个策略的性能指标

    def add_strategy(self, strategy: BaseStrategy, strategy_name: Optional[str] = None) -> None:
        """
        添加策略到回测系统

        :param strategy: 策略对象，必须是BaseStrategy的子类
        :param strategy_name: 策略名称，如果不提供则使用策略类名
        """
        if strategy_name is None:
            strategy_name = strategy.__class__.__name__

        # 生成信号
        signals = strategy.generate_signals(self.df)

        # 运行回测
        result, _ = run_backtest(self.df, signals, slippage=self.slippage, fee=self.fee)

        # 存储结果
        self.results[strategy_name] = result

        # 计算性能指标
        self._calculate_performance_metrics(strategy_name)

    def _calculate_performance_metrics(self, strategy_name: str) -> None:
        """
        计算策略的性能指标

        :param strategy_name: 策略名称
        """
        result = self.results[strategy_name]

        # 计算累计收益率
        total_return = result["equity"].iloc[-1] - 1.0

        # 计算年化收益率（假设一年有252个交易日）
        days = (result.index[-1] - result.index[0]).days
        if days > 0:
            annual_return = (1 + total_return) ** (252 / days) - 1
        else:
            annual_return = 0

        # 计算最大回撤
        cumulative_max = result["equity"].cummax()
        drawdown = (result["equity"] - cumulative_max) / cumulative_max
        max_drawdown = drawdown.min()

        # 计算夏普比率（假设无风险利率为0）
        daily_returns = result["strategy_return"]
        if len(daily_returns) > 1 and daily_returns.std() != 0:
            sharpe_ratio = np.sqrt(252) * daily_returns.mean() / daily_returns.std()
        else:
            sharpe_ratio = 0

        # 计算胜率
        trades = result[result["trade"] != 0]
        if len(trades) > 0:
            win_rate = len(trades[trades["pnl"] > 0]) / len(trades)
        else:
            win_rate = 0

        # 计算盈亏比
        if len(trades[trades["pnl"] > 0]) > 0 and len(trades[trades["pnl"] < 0]) > 0:
            avg_win = trades[trades["pnl"] > 0]["pnl"].mean()
            avg_loss = abs(trades[trades["pnl"] < 0]["pnl"].mean())
            profit_loss_ratio = avg_win / avg_loss if avg_loss != 0 else 0
        else:
            profit_loss_ratio = 0

        # 存储性能指标
        self.performance_metrics[strategy_name] = {
            "总收益率": total_return,
            "年化收益率": annual_return,
            "最大回撤": max_drawdown,
            "夏普比率": sharpe_ratio,
            "胜率": win_rate,
            "盈亏比": profit_loss_ratio,
            "交易次数": len(trades)
        }

    def get_performance_comparison(self) -> pd.DataFrame:
        """
        获取所有策略的性能指标比较表

        :return: 包含所有策略性能指标的DataFrame
        """
        if not self.performance_metrics:
            return pd.DataFrame()

        return pd.DataFrame(self.performance_metrics).T

    def get_equity_curves(self) -> pd.DataFrame:
        """
        获取所有策略的净值曲线

        :return: 包含所有策略净值曲线的DataFrame
        """
        if not self.results:
            return pd.DataFrame()

        equity_curves = pd.DataFrame(index=self.df.index)

        for strategy_name, result in self.results.items():
            equity_curves[strategy_name] = result["equity"]

        return equity_curves
