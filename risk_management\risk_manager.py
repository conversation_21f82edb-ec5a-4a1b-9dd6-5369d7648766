# risk_management/risk_manager.py

from typing import Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from risk_management.base import RiskManager
from risk_management.stop_loss import StopLossManager
from risk_management.position_sizing import PositionSizeManager

class ComprehensiveRiskManager(RiskManager):
    """
    综合风控管理器，结合止损止盈和仓位管理功能
    """
    def __init__(self, config: Dict):
        """
        初始化综合风控管理器

        :param config: 风控配置字典
        """
        super().__init__(config)

        # 创建子管理器
        self.stop_loss_manager = StopLossManager(config)
        self.position_size_manager = PositionSizeManager(config)

    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                          price: float, signal: int, symbol: str) -> bool:
        """
        检查是否可以入场，综合考虑各种风控条件

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许入场
        """
        # 同时满足所有子管理器的入场条件
        return (self.stop_loss_manager.check_entry_signal(df, time, price, signal, symbol) and
                self.position_size_manager.check_entry_signal(df, time, price, signal, symbol))

    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                         price: float, signal: int, symbol: str) -> (bool, str):
        """
        检查是否需要出场，综合考虑各种风控条件
        返回：(是否允许出场, 原因字符串)
        """
        position = self.get_position(symbol)
        entry_price = position["entry_price"]
        entry_time = position["entry_time"]
        pnl_pct = (price - entry_price) / entry_price
        # 检查止损管理器
        stop_loss_exit, stop_loss_reason = self.stop_loss_manager.check_exit_signal(df, time, price, signal, symbol)
        if stop_loss_exit:
            return True, f"止损管理器: {stop_loss_reason}"
        # 检查仓位管理器
        position_size_exit, position_size_reason = self.position_size_manager.check_exit_signal(df, time, price, signal, symbol) if hasattr(self.position_size_manager, 'check_exit_signal') else (False, "")
        if position_size_exit:
            return True, f"仓位管理器: {position_size_reason}"
        # 策略信号为-1时的提示
        if signal == -1 and self.has_position(symbol):
            return False, "策略信号为-1，但未满足风控出场条件"
        return False, "未满足任何出场条件"

    def calculate_position_size(self, df: pd.DataFrame, time: pd.Timestamp,
                               price: float, symbol: str) -> float:
        """
        计算仓位大小

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param symbol: 交易对
        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        # 使用仓位管理器计算仓位大小
        return self.position_size_manager.calculate_position_size(df, time, price, symbol)

    def on_trade_completed(self, entry_time: pd.Timestamp, entry_price: float,
                          exit_time: pd.Timestamp, exit_price: float,
                          pnl: float, symbol: str) -> None:
        """
        交易完成后的回调

        :param entry_time: 入场时间
        :param entry_price: 入场价格
        :param exit_time: 出场时间
        :param exit_price: 出场价格
        :param pnl: 盈亏金额
        :param symbol: 交易对
        """
        super().on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, symbol)

        # 同步更新子管理器的状态
        self.stop_loss_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, symbol)
        self.position_size_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, symbol)

        # 同步资金状态
        self.stop_loss_manager.current_capital = self.current_capital
        self.position_size_manager.current_capital = self.current_capital

    def add_position(self, symbol: str, size: float, entry_price: float,
                    entry_time: pd.Timestamp) -> None:
        """
        添加持仓，同步到所有子管理器

        :param symbol: 交易对
        :param size: 仓位大小
        :param entry_price: 入场价格
        :param entry_time: 入场时间
        """
        # 添加到主管理器
        super().add_position(symbol, size, entry_price, entry_time)

        # 同步到所有子管理器
        self.stop_loss_manager.add_position(symbol, size, entry_price, entry_time)
        self.position_size_manager.add_position(symbol, size, entry_price, entry_time)

    def remove_position(self, symbol: str) -> None:
        """
        移除持仓，同步到所有子管理器

        :param symbol: 交易对
        """
        # 从主管理器移除
        super().remove_position(symbol)

        # 从所有子管理器移除
        self.stop_loss_manager.remove_position(symbol)
        self.position_size_manager.remove_position(symbol)
