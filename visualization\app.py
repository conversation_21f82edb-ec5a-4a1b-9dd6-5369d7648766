# visualization/app.py
"""
CTA策略交易系统 - 可视化模块主应用
"""

import streamlit as st

# 导入配置
from .config import apply_page_config, apply_custom_css, NAVIGATION_PAGES

# 导入页面模块
from .pages import (
    show_single_strategy_backtest,
    show_multi_strategy_comparison,
    show_risk_managed_backtest,
    show_trade_records,
    show_parameter_config
)

# 导入工具
from .utils import SessionManager

def main():
    """主应用函数"""
    # 应用页面配置
    apply_page_config()
    apply_custom_css()

    # 主标题
    st.markdown('<h1 class="main-header">📈 CTA策略交易系统</h1>', unsafe_allow_html=True)

    # 侧边栏导航
    _create_sidebar()

    # 获取当前页面
    current_page = SessionManager.get_current_page()

    # 根据选择的页面显示对应内容
    _render_page(current_page)

def _create_sidebar():
    """创建侧边栏导航"""
    st.sidebar.title("🎛️ 控制面板")

    # 页面选择
    current_page = SessionManager.get_current_page()

    page = st.sidebar.selectbox(
        "选择功能模块",
        NAVIGATION_PAGES,
        index=NAVIGATION_PAGES.index(current_page) if current_page in NAVIGATION_PAGES else 0
    )

    # 更新当前页面
    if page != current_page:
        SessionManager.set_current_page(page)
        SessionManager.rerun()

    # 添加分隔线
    st.sidebar.markdown("---")

    # 系统信息
    _display_system_info()

    # 快捷操作
    _display_quick_actions()

def _display_system_info():
    """显示系统信息"""
    with st.sidebar.expander("ℹ️ 系统信息"):
        st.markdown("""
        **版本**: v1.0.0
        **模块**: 可视化系统
        **状态**: 运行中 🟢
        **最后更新**: 2025-05-25
        """)

def _display_quick_actions():
    """显示快捷操作"""
    st.sidebar.subheader("⚡ 快捷操作")

    col1, col2 = st.sidebar.columns(2)

    with col1:
        if st.button("🔄 刷新数据", use_container_width=True):
            # 清除缓存数据
            _clear_cache()
            st.success("数据已刷新")

    with col2:
        if st.button("🗑️ 清除缓存", use_container_width=True):
            # 清除所有会话状态
            SessionManager.clear()
            st.success("缓存已清除")
            SessionManager.rerun()

def _clear_cache():
    """清除缓存数据"""
    # 清除特定的缓存数据，保留页面状态
    keys_to_clear = [
        'backtest_result',
        'multi_strategy_result',
        'risk_backtest_result'
    ]

    for key in keys_to_clear:
        SessionManager.delete(key)

def _render_page(page: str):
    """
    渲染指定页面

    Args:
        page: 页面名称
    """
    try:
        if page == "📊 策略回测":
            show_single_strategy_backtest()
        elif page == "📈 多策略比较":
            show_multi_strategy_comparison()
        elif page == "🛡️ 风控回测":
            show_risk_managed_backtest()
        elif page == "📋 交易记录":
            show_trade_records()
        elif page == "⚙️ 参数配置":
            show_parameter_config()
        else:
            st.error(f"未知页面: {page}")

    except Exception as e:
        st.error(f"页面加载失败: {e}")
        st.exception(e)

if __name__ == "__main__":
    main()
