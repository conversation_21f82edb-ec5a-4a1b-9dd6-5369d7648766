# visualization/components/charts.py
"""
图表组件模块
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, Any

from ..config import get_chart_colors

def plot_candlestick_with_signals(df_result: pd.DataFrame, signals: pd.DataFrame, config: Dict) -> None:
    """
    绘制K线图和交易信号
    
    Args:
        df_result: 回测结果数据
        signals: 信号数据
        config: 策略配置
    """
    try:
        # 创建子图
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=('价格走势与交易信号', '持仓状态'),
            row_heights=[0.7, 0.3]
        )
        
        # K线图
        fig.add_trace(
            go.Candlestick(
                x=df_result.index,
                open=df_result['open'],
                high=df_result['high'],
                low=df_result['low'],
                close=df_result['close'],
                name="K线"
            ),
            row=1, col=1
        )
        
        # 添加买卖信号
        buy_signals = df_result[df_result['position'].diff() == 1]
        sell_signals = df_result[df_result['position'].diff() == -1]
        
        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals.index,
                    y=buy_signals['close'],
                    mode='markers',
                    marker=dict(symbol='triangle-up', size=12, color='green'),
                    name='买入信号'
                ),
                row=1, col=1
            )
        
        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals.index,
                    y=sell_signals['close'],
                    mode='markers',
                    marker=dict(symbol='triangle-down', size=12, color='red'),
                    name='卖出信号'
                ),
                row=1, col=1
            )
        
        # 持仓状态
        fig.add_trace(
            go.Scatter(
                x=df_result.index,
                y=df_result['position'],
                mode='lines',
                name='持仓状态',
                line=dict(color='blue', width=2)
            ),
            row=2, col=1
        )
        
        # 更新布局
        fig.update_layout(
            title=f"{config.get('symbol', 'Unknown')} - {config.get('strategy_type', 'Unknown')} 策略",
            xaxis_title="时间",
            height=800,
            showlegend=True
        )
        
        fig.update_yaxes(title_text="价格", row=1, col=1)
        fig.update_yaxes(title_text="持仓", row=2, col=1)
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制K线图失败: {e}")

def plot_equity_curve(df_result: pd.DataFrame) -> None:
    """
    绘制净值曲线
    
    Args:
        df_result: 回测结果数据
    """
    try:
        fig = go.Figure()
        
        # 净值曲线
        fig.add_trace(
            go.Scatter(
                x=df_result.index,
                y=df_result['equity_curve'],
                mode='lines',
                name='净值曲线',
                line=dict(color='blue', width=2)
            )
        )
        
        # 添加基准线
        fig.add_hline(y=1.0, line_dash="dash", line_color="gray", annotation_text="基准线")
        
        # 更新布局
        fig.update_layout(
            title="策略净值曲线",
            xaxis_title="时间",
            yaxis_title="净值",
            height=500,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制净值曲线失败: {e}")

def plot_multi_equity_curves(equity_curves: pd.DataFrame) -> None:
    """
    绘制多策略净值曲线对比
    
    Args:
        equity_curves: 多策略净值曲线数据
    """
    try:
        fig = go.Figure()
        
        # 为每个策略添加净值曲线
        colors = get_chart_colors(len(equity_curves.columns))
        for i, strategy_name in enumerate(equity_curves.columns):
            color = colors[i]
            fig.add_trace(
                go.Scatter(
                    x=equity_curves.index,
                    y=equity_curves[strategy_name],
                    mode='lines',
                    name=strategy_name,
                    line=dict(color=color, width=2)
                )
            )
        
        # 添加基准线
        fig.add_hline(y=1.0, line_dash="dash", line_color="gray", annotation_text="基准线")
        
        # 更新布局
        fig.update_layout(
            title="多策略净值曲线对比",
            xaxis_title="时间",
            yaxis_title="净值",
            height=600,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制净值曲线对比失败: {e}")

def plot_drawdown_comparison(backtest_results: Dict[str, pd.DataFrame]) -> None:
    """
    绘制策略回撤对比
    
    Args:
        backtest_results: 回测结果字典
    """
    try:
        fig = go.Figure()
        
        colors = get_chart_colors(len(backtest_results))
        for i, (strategy_name, result) in enumerate(backtest_results.items()):
            equity = result["equity"]
            cumulative_max = equity.cummax()
            drawdown = (equity - cumulative_max) / cumulative_max
            
            color = colors[i]
            fig.add_trace(
                go.Scatter(
                    x=result.index,
                    y=drawdown,
                    mode='lines',
                    name=f"{strategy_name} 回撤",
                    line=dict(color=color, width=2)
                )
            )
        
        fig.update_layout(
            title="策略回撤曲线对比",
            xaxis_title="时间",
            yaxis_title="回撤",
            height=500,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制回撤对比失败: {e}")

def plot_trade_pnl_distribution(trade_log: list) -> None:
    """
    绘制交易盈亏分布图
    
    Args:
        trade_log: 交易记录列表
    """
    if not trade_log:
        st.info("暂无交易记录")
        return
    
    try:
        df_trades = pd.DataFrame(trade_log)
        
        if 'pnl' not in df_trades.columns:
            st.warning("交易记录中缺少盈亏数据")
            return
        
        fig = go.Figure()
        
        colors = ['green' if x > 0 else 'red' for x in df_trades['pnl']]
        
        fig.add_trace(
            go.Bar(
                x=list(range(len(df_trades))),
                y=df_trades['pnl'],
                marker_color=colors,
                name='每笔交易盈亏'
            )
        )
        
        fig.update_layout(
            title="每笔交易盈亏分布",
            xaxis_title="交易序号",
            yaxis_title="盈亏(USDT)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制盈亏分布图失败: {e}")
