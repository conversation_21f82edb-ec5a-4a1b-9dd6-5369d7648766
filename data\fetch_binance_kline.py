# cta_mvp/data/fetch_binance_kline.py

import ccxt
import pandas as pd
from datetime import datetime

def fetch_ohlcv(symbol: str, timeframe: str = "1h", limit: int = 500) -> pd.DataFrame:
    """
    从Binance交易所获取指定交易对的K线数据。
    
    :param symbol: 交易对名称，例如 "BTC/USDT"。
    :param timeframe: K线时间周期，默认为 "1h"（1小时）。
    :param limit: 获取的K线数量，默认为500条。
    :return: 包含K线数据的DataFrame，索引为时间戳，列包括开盘价、最高价、最低价、收盘价和成交量。
    """
    exchange = ccxt.binance()  # 创建Binance交易所对象
    ohlcv = exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)  # 获取K线数据

    # 将K线数据转换为DataFrame
    df = pd.DataFrame(ohlcv, columns=["timestamp", "open", "high", "low", "close", "volume"])
    df["timestamp"] = pd.to_datetime(df["timestamp"], unit="ms")  # 将时间戳转换为日期时间格式
    df.set_index("timestamp", inplace=True)  # 将时间戳设为索引
    return df