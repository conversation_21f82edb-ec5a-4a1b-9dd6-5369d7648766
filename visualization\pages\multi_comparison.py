# visualization/pages/multi_comparison.py
"""
多策略比较页面
"""

import streamlit as st
import pandas as pd
from typing import Dict, List, Any

# 导入项目模块
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.multi_strategy_backtest import MultiStrategyBacktest

# 导入可视化组件
from ..components import (
    plot_multi_equity_curves,
    plot_drawdown_comparison,
    display_performance_table,
    display_strategy_ranking,
    create_multi_strategy_selector,
    create_parameter_form
)
from ..utils import load_strategy_configs, SessionManager
from ..utils.data_loader import get_strategy_display_name

def show_multi_strategy_comparison():
    """多策略比较页面"""
    st.header("📈 多策略比较分析")

    # 加载策略配置
    configs = load_strategy_configs()
    if not configs:
        return

    # 创建两列布局
    col1, col2 = st.columns([1, 2])

    with col1:
        st.subheader("🎯 策略选择")

        # 多策略选择器
        selected_configs = create_multi_strategy_selector(configs)
        if not selected_configs:
            return

        # 参数表单
        params = create_parameter_form()

        # 执行比较按钮
        if st.button("🚀 执行多策略比较", type="primary"):
            with st.spinner("正在执行多策略比较..."):
                _execute_multi_strategy_comparison(selected_configs, params)

    with col2:
        st.subheader("📊 比较结果")

        # 显示比较结果
        result = SessionManager.get_multi_strategy_result()
        if result:
            _display_multi_strategy_results(result)
        else:
            st.info("请选择策略并点击执行比较按钮")

def _execute_multi_strategy_comparison(configs: List[Dict], params: Dict) -> None:
    """
    执行多策略比较

    Args:
        configs: 策略配置列表
        params: 回测参数
    """
    try:
        # 获取第一个配置中的交易对和时间周期（假设所有策略使用相同的数据）
        symbol = configs[0]["symbol"]
        timeframe = configs[0]["timeframe"]

        # 获取数据
        df = fetch_ohlcv(symbol, timeframe, limit=params['limit'])

        # 创建多策略回测对象
        backtest = MultiStrategyBacktest(df)

        # 添加所有策略
        for config in configs:
            # 更新配置参数
            config = config.copy()
            config.update(params)

            strategy = StrategyFactory.create(config)
            strategy.validate_config()

            # 使用display_name作为策略名称
            strategy_name = get_strategy_display_name(config)

            backtest.add_strategy(strategy, strategy_name)

        # 获取所有策略的净值曲线
        equity_curves = backtest.get_equity_curves()

        # 获取所有策略的性能指标
        performance_metrics = backtest.get_performance_comparison()

        # 保存结果
        result = {
            'equity_curves': equity_curves,
            'performance_metrics': performance_metrics,
            'backtest_results': backtest.results,
            'configs': configs,
            'params': params
        }
        SessionManager.set_multi_strategy_result(result)

        st.success("多策略比较执行完成！")

    except Exception as e:
        st.error(f"多策略比较执行失败: {e}")

def _display_multi_strategy_results(result: Dict[str, Any]) -> None:
    """
    显示多策略比较结果

    Args:
        result: 比较结果字典
    """
    equity_curves = result['equity_curves']
    performance_metrics = result['performance_metrics']
    backtest_results = result['backtest_results']

    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "📈 净值曲线对比",
        "📊 性能指标对比",
        "📉 回撤分析",
        "🏆 策略排名"
    ])

    with tab1:
        st.subheader("策略净值曲线对比")
        plot_multi_equity_curves(equity_curves)

        # 显示基本统计信息
        _display_equity_statistics(equity_curves)

    with tab2:
        st.subheader("策略性能指标对比")
        display_performance_table(performance_metrics)

        # 显示关键指标对比图
        _display_metrics_comparison_charts(performance_metrics)

    with tab3:
        st.subheader("策略回撤分析")
        plot_drawdown_comparison(backtest_results)

        # 显示回撤统计
        _display_drawdown_statistics(backtest_results)

    with tab4:
        st.subheader("策略综合排名")
        display_strategy_ranking(performance_metrics)

def _display_equity_statistics(equity_curves: pd.DataFrame) -> None:
    """
    显示净值曲线统计信息

    Args:
        equity_curves: 净值曲线数据
    """
    st.subheader("📊 净值统计")

    # 计算统计指标
    final_values = equity_curves.iloc[-1]
    max_values = equity_curves.max()
    min_values = equity_curves.min()

    # 创建统计表格
    stats_data = {
        '策略': equity_curves.columns,
        '最终净值': final_values.values,
        '最高净值': max_values.values,
        '最低净值': min_values.values,
        '净值波动': (max_values - min_values).values
    }

    stats_df = pd.DataFrame(stats_data)
    st.dataframe(stats_df, use_container_width=True)

def _display_metrics_comparison_charts(performance_metrics: pd.DataFrame) -> None:
    """
    显示指标对比图表

    Args:
        performance_metrics: 性能指标数据
    """
    if performance_metrics.empty:
        return

    import plotly.graph_objects as go

    metrics_to_plot = ["总收益率", "最大回撤", "夏普比率", "胜率"]

    for metric in metrics_to_plot:
        if metric in performance_metrics.columns:
            fig = go.Figure()

            values = performance_metrics[metric]
            if metric in ["总收益率", "最大回撤", "胜率"]:
                values = values * 100  # 转换为百分比

            colors = ['green' if v > 0 else 'red' for v in values] if metric == "总收益率" else 'blue'

            fig.add_trace(
                go.Bar(
                    x=performance_metrics.index,
                    y=values,
                    name=metric,
                    marker_color=colors
                )
            )

            y_title = f"{metric}(%)" if metric in ["总收益率", "最大回撤", "胜率"] else metric

            fig.update_layout(
                title=f"{metric}对比",
                xaxis_title="策略",
                yaxis_title=y_title,
                height=400
            )

            st.plotly_chart(fig, use_container_width=True)

def _display_drawdown_statistics(backtest_results: Dict) -> None:
    """
    显示回撤统计信息

    Args:
        backtest_results: 回测结果字典
    """
    st.subheader("📉 回撤统计")

    drawdown_stats = []

    for strategy_name, result in backtest_results.items():
        equity = result["equity"]
        cumulative_max = equity.cummax()
        drawdown = (equity - cumulative_max) / cumulative_max

        max_drawdown = drawdown.min()
        avg_drawdown = drawdown.mean()
        drawdown_duration = len(drawdown[drawdown < 0])

        drawdown_stats.append({
            '策略': strategy_name,
            '最大回撤': f"{max_drawdown:.2%}",
            '平均回撤': f"{avg_drawdown:.2%}",
            '回撤期数': drawdown_duration
        })

    stats_df = pd.DataFrame(drawdown_stats)
    st.dataframe(stats_df, use_container_width=True)
