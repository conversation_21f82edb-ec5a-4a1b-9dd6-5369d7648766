# visualization/utils/data_loader.py
"""
数据加载工具函数
"""

import json
import os
import pandas as pd
import streamlit as st
from typing import List, Dict, Optional

def load_strategy_configs(config_dir: str = "config/strategies") -> List[Dict]:
    """
    加载所有策略配置文件
    
    Args:
        config_dir: 配置文件目录路径
        
    Returns:
        策略配置列表
    """
    configs = []
    
    if not os.path.exists(config_dir):
        st.error(f"配置目录不存在: {config_dir}")
        return configs
    
    for filename in os.listdir(config_dir):
        if filename.endswith('.json'):
            try:
                filepath = os.path.join(config_dir, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    config['config_file'] = filename
                    configs.append(config)
            except Exception as e:
                st.error(f"加载配置文件 {filename} 失败: {e}")
    
    return configs

def load_trade_records(csv_path: str = "trade_log.csv") -> Optional[pd.DataFrame]:
    """
    加载交易记录
    
    Args:
        csv_path: 交易记录CSV文件路径
        
    Returns:
        交易记录DataFrame，如果文件不存在返回None
    """
    try:
        if os.path.exists(csv_path):
            df = pd.read_csv(csv_path, parse_dates=["entry_time", "exit_time"])
            return df
        else:
            st.warning(f"交易记录文件不存在: {csv_path}")
            return None
    except Exception as e:
        st.error(f"加载交易记录失败: {e}")
        return None

def validate_config(config: Dict) -> bool:
    """
    验证策略配置的完整性
    
    Args:
        config: 策略配置字典
        
    Returns:
        配置是否有效
    """
    required_fields = ['strategy_type', 'symbol', 'timeframe']
    
    for field in required_fields:
        if field not in config:
            st.error(f"配置文件缺少必要字段: {field}")
            return False
    
    return True

def get_strategy_display_name(config: Dict) -> str:
    """
    获取策略显示名称
    
    Args:
        config: 策略配置字典
        
    Returns:
        策略显示名称
    """
    display_name = config.get('display_name')
    if display_name:
        return display_name
    
    strategy_type = config.get('strategy_type', 'Unknown')
    config_file = config.get('config_file', '')
    
    return f"{strategy_type} ({config_file})"

def format_strategy_options(configs: List[Dict]) -> Dict[str, int]:
    """
    格式化策略选项用于UI显示
    
    Args:
        configs: 策略配置列表
        
    Returns:
        策略名称到索引的映射字典
    """
    strategy_options = {}
    
    for i, config in enumerate(configs):
        display_name = get_strategy_display_name(config)
        strategy_options[display_name] = i
    
    return strategy_options
