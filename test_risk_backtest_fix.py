#!/usr/bin/env python3
"""
测试风控回测修复
"""

import pandas as pd
import numpy as np
from backtest.risk_managed_backtest import run_backtest_with_risk_management

def test_risk_backtest_basic():
    """测试风控回测基本功能"""
    print("测试风控回测基本功能...")

    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='1H')
    np.random.seed(42)

    # 生成模拟价格数据
    price_changes = np.random.normal(0, 0.01, 100)
    prices = 100 * np.exp(np.cumsum(price_changes))

    df = pd.DataFrame({
        'open': prices * 0.999,
        'high': prices * 1.002,
        'low': prices * 0.998,
        'close': prices,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)

    # 创建简单的信号数据
    signals = pd.DataFrame({
        'signal': [1 if i % 20 == 0 else 0 for i in range(100)],
        'position': [1 if i % 20 == 0 else 0 for i in range(100)]
    }, index=dates)

    # 风控配置
    risk_config = {
        "initial_capital": 10000.0,
        "position_ratio": 1.0,
        "max_positions": 1,
        "stop_loss_pct": 0.05,
        "take_profit_pct": 0.10,
        "position_sizing_method": "fixed_ratio"
    }

    try:
        # 执行风控回测
        df_result, trade_log = run_backtest_with_risk_management(df, signals, risk_config)

        print(f"✅ 风控回测执行成功")
        print(f"   - 数据行数: {len(df_result)}")
        print(f"   - 交易次数: {len(trade_log)}")
        print(f"   - 结果列: {list(df_result.columns)}")

        # 检查关键列是否存在
        required_columns = ['equity', 'equity_curve']
        for col in required_columns:
            if col in df_result.columns:
                print(f"   - ✅ 找到列: {col}")
            else:
                print(f"   - ❌ 缺少列: {col}")

        # 检查净值数据
        if 'equity' in df_result.columns:
            final_equity = df_result['equity'].iloc[-1]
            print(f"   - 最终净值: {final_equity:.4f}")

        return True

    except Exception as e:
        print(f"❌ 风控回测执行失败: {e}")
        import traceback
        print("详细错误:")
        print(traceback.format_exc())
        return False

def test_risk_config_building():
    """测试风控配置构建"""
    print("\n测试风控配置构建...")

    from visualization.pages.risk_backtest import _build_risk_config

    backtest_params = {
        'initial_capital': 10000,
        'slippage': 0.0005,
        'fee': 0.001
    }

    risk_params = {
        'stop_loss_enabled': True,
        'stop_loss_pct': 0.05,
        'take_profit_enabled': True,
        'take_profit_pct': 0.10,
        'max_position_size': 0.8,
        'max_drawdown_enabled': True,
        'max_drawdown_pct': 0.20
    }

    try:
        risk_config = _build_risk_config(backtest_params, risk_params)
        print(f"✅ 风控配置构建成功")
        print(f"   - 配置内容: {risk_config}")

        # 检查关键配置
        expected_keys = ['initial_capital', 'position_ratio', 'stop_loss_pct', 'take_profit_pct']
        for key in expected_keys:
            if key in risk_config:
                value = risk_config[key]
                print(f"   - ✅ {key}: {value} (类型: {type(value).__name__})")
            else:
                print(f"   - ❌ 缺少配置: {key}")

        return True

    except Exception as e:
        print(f"❌ 风控配置构建失败: {e}")
        import traceback
        print("详细错误:")
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🧪 风控回测修复测试")
    print("=" * 50)

    # 测试基本功能
    test1_result = test_risk_backtest_basic()

    # 测试配置构建
    test2_result = test_risk_config_building()

    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"   - 基本功能测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   - 配置构建测试: {'✅ 通过' if test2_result else '❌ 失败'}")

    if test1_result and test2_result:
        print("\n🎉 所有测试通过！风控回测模块修复成功！")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
