# cta_mvp/execution/simulated_executor.py

import csv
from pathlib import Path

def simulate_execution(df, log_path="trade_log.csv", initial_capital=10000.0):
    """
    模拟交易执行

    :param df: 包含交易信号的DataFrame
    :param log_path: 交易日志保存路径
    :param initial_capital: 初始资金
    :return: 交易日志列表
    """
    position = 0
    entry_price = 0
    entry_time = None
    trade_log = []

    # 检查策略类型
    has_negative_position = (df["position"] < 0).any()

    # 记录初始资金
    current_capital = initial_capital

    print(f"初始资金: {initial_capital:.2f} USDT")

    for time, row in df.iterrows():
        # 对于布林带突破等策略，position可以是-1（表示卖出信号）
        if row["position"] == 1 and position == 0:
            # 开仓
            entry_price = row["close"]
            entry_time = time
            position = 1

            # 计算仓位大小（默认使用100%资金）
            position_ratio = 1.0
            position_size = current_capital * position_ratio

            print(f"[{time}] 💹 买入 @ {entry_price:.2f}，仓位: {position_ratio:.2%}，金额: {position_size:.2f} USDT")

        # 对于双均线等策略，position只有0和1，需要检测从1变为0的情况作为卖出信号
        elif has_negative_position and row["position"] == -1 and position == 1:
            # 布林带策略的平仓信号
            exit_price = row["close"]
            exit_time = time

            # 计算盈亏（按照固定比例仓位计算）
            position_size = current_capital * 1.0  # 假设使用100%资金
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            position = 0
            print(f"[{time}] 💼 卖出 @ {exit_price:.2f}，盈亏: {pnl:.2f} USDT，当前资金: {current_capital:.2f} USDT")

            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "position_size": position_size,
                "capital": current_capital
            })

        elif not has_negative_position and row["position"] == 0 and position == 1:
            # 双均线策略的平仓信号
            exit_price = row["close"]
            exit_time = time

            # 计算盈亏（按照固定比例仓位计算）
            position_size = current_capital * 1.0  # 假设使用100%资金
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            position = 0
            print(f"[{time}] 💼 卖出 @ {exit_price:.2f}，盈亏: {pnl:.2f} USDT，当前资金: {current_capital:.2f} USDT")

            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "position_size": position_size,
                "capital": current_capital
            })

    if position == 1:
        print("⚠️ 持仓未平仓，模拟盘结束时仍持有头寸")

    # 计算最终收益率
    final_return = (current_capital / initial_capital - 1) * 100
    print(f"\n📊 最终资金: {current_capital:.2f} USDT，收益率: {final_return:.2f}%")

    # 写入CSV文件
    if trade_log:
        log_file = Path(log_path)
        with log_file.open("w", newline="") as f:
            writer = csv.DictWriter(f, fieldnames=trade_log[0].keys())
            writer.writeheader()
            writer.writerows(trade_log)
        print(f"\n✅ 交易日志保存至：{log_path}")

    return trade_log
