# visualization/config.py
"""
可视化模块的配置文件
包含页面配置、样式定义、常量等
"""

import streamlit as st

# 页面配置
PAGE_CONFIG = {
    "page_title": "CTA策略交易系统",
    "page_icon": "📈",
    "layout": "wide",
    "initial_sidebar_state": "expanded"
}

# 页面导航配置
NAVIGATION_PAGES = [
    "📊 策略回测",
    "📈 多策略比较", 
    "🛡️ 风控回测",
    "📋 交易记录",
    "⚙️ 参数配置"
]

# CSS样式
CUSTOM_CSS = """
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
    }
    .success-metric {
        border-left-color: #28a745;
    }
    .warning-metric {
        border-left-color: #ffc107;
    }
    .danger-metric {
        border-left-color: #dc3545;
    }
    .info-metric {
        border-left-color: #17a2b8;
    }
    .sidebar-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .chart-container {
        background-color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }
</style>
"""

# 图表配置
CHART_CONFIG = {
    "colors": {
        "primary": "#1f77b4",
        "success": "#28a745", 
        "warning": "#ffc107",
        "danger": "#dc3545",
        "info": "#17a2b8"
    },
    "plotly_colors": [
        "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd",
        "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"
    ]
}

# 默认参数
DEFAULT_PARAMS = {
    "limit": 500,
    "initial_capital": 10000,
    "fee": 0.001,
    "slippage": 0.0005
}

# 性能指标配置
METRICS_CONFIG = {
    "percentage_columns": ["总收益率", "年化收益率", "最大回撤", "胜率"],
    "numeric_columns": ["夏普比率", "平均盈亏比", "综合评分"],
    "ranking_weights": {
        "总收益率": 0.3,
        "夏普比率": 0.25,
        "胜率": 0.2,
        "最大回撤": 0.15,
        "平均盈亏比": 0.1
    }
}

def apply_page_config():
    """应用页面配置"""
    st.set_page_config(**PAGE_CONFIG)

def apply_custom_css():
    """应用自定义CSS样式"""
    st.markdown(CUSTOM_CSS, unsafe_allow_html=True)

def get_chart_colors(n_colors=None):
    """获取图表颜色列表"""
    colors = CHART_CONFIG["plotly_colors"]
    if n_colors:
        return colors[:n_colors] if n_colors <= len(colors) else colors * ((n_colors // len(colors)) + 1)
    return colors
