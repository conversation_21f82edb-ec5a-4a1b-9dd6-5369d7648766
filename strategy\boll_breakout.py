import pandas as pd
from strategy.base import BaseStrategy, StrategyConfigError

class BollBreakoutStrategy(BaseStrategy):
    """
    布林带突破策略实现类。
    
    参数：
        config (dict): 策略配置，需包含'window'和'std_dev'。
    属性：
        window (int): 布林带窗口期
        std_dev (float): 标准差倍数
    """
    def __init__(self, config):
        """
        初始化布林带突破策略，自动校验参数。
        参数:
            config (dict): 策略配置，需包含'window'和'std_dev'。
        """
        super().__init__(config)
        self.window = config["window"]
        self.std_dev = config["std_dev"]
        self.validate_config()  # 自动校验参数

    def validate_config(self):
        """
        校验策略参数的有效性。
        window必须为整数，std_dev必须为数字。
        """
        if not isinstance(self.window, int):
            raise StrategyConfigError("window 必须为整数")
        if not isinstance(self.std_dev, (int, float)):
            raise StrategyConfigError("std_dev 必须为数字")

    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        根据收盘价计算布林带上下轨，生成买卖信号。
        买入信号: 收盘价上穿上轨。
        卖出信号: 收盘价下穿下轨。
        
        参数:
            df (pd.DataFrame): 包含'close'列的行情数据
        返回:
            pd.DataFrame: 新增'mid', 'std', 'upper', 'lower', 'signal', 'position'列
        """
        df = df.copy()
        df["mid"] = df["close"].rolling(self.window).mean()
        df["std"] = df["close"].rolling(self.window).std()
        df["upper"] = df["mid"] + self.std_dev * df["std"]
        df["lower"] = df["mid"] - self.std_dev * df["std"]

        df["signal"] = 0
        df.loc[df["close"] > df["upper"], "signal"] = 1
        df.loc[df["close"] < df["lower"], "signal"] = -1
        df["position"] = df["signal"].diff()
        return df
