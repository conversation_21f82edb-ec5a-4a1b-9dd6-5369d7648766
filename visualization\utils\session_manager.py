# visualization/utils/session_manager.py
"""
Streamlit会话状态管理工具
"""

import streamlit as st
from typing import Any, Dict, Optional

class SessionManager:
    """会话状态管理器"""

    @staticmethod
    def get(key: str, default: Any = None) -> Any:
        """
        获取会话状态值

        Args:
            key: 状态键
            default: 默认值

        Returns:
            状态值
        """
        return st.session_state.get(key, default)

    @staticmethod
    def set(key: str, value: Any) -> None:
        """
        设置会话状态值

        Args:
            key: 状态键
            value: 状态值
        """
        st.session_state[key] = value

    @staticmethod
    def has(key: str) -> bool:
        """
        检查是否存在指定的会话状态

        Args:
            key: 状态键

        Returns:
            是否存在
        """
        return key in st.session_state

    @staticmethod
    def delete(key: str) -> None:
        """
        删除会话状态

        Args:
            key: 状态键
        """
        if key in st.session_state:
            del st.session_state[key]

    @staticmethod
    def clear() -> None:
        """清空所有会话状态"""
        st.session_state.clear()

    @staticmethod
    def get_backtest_result() -> Optional[Dict]:
        """获取单策略回测结果"""
        return SessionManager.get('backtest_result')

    @staticmethod
    def set_backtest_result(result: Dict) -> None:
        """设置单策略回测结果"""
        SessionManager.set('backtest_result', result)

    @staticmethod
    def get_multi_strategy_result() -> Optional[Dict]:
        """获取多策略比较结果"""
        return SessionManager.get('multi_strategy_result')

    @staticmethod
    def set_multi_strategy_result(result: Dict) -> None:
        """设置多策略比较结果"""
        SessionManager.set('multi_strategy_result', result)

    @staticmethod
    def get_risk_backtest_result() -> Optional[Dict]:
        """获取风控回测结果"""
        return SessionManager.get('risk_backtest_result')

    @staticmethod
    def set_risk_backtest_result(result: Dict) -> None:
        """设置风控回测结果"""
        SessionManager.set('risk_backtest_result', result)

    @staticmethod
    def get_current_page() -> str:
        """获取当前页面"""
        return SessionManager.get('current_page', '📊 策略回测')

    @staticmethod
    def set_current_page(page: str) -> None:
        """设置当前页面"""
        SessionManager.set('current_page', page)

    @staticmethod
    def rerun() -> None:
        """
        重新运行应用，兼容不同版本的Streamlit
        """
        try:
            st.rerun()
        except AttributeError:
            # 兼容旧版本Streamlit
            st.experimental_rerun()
