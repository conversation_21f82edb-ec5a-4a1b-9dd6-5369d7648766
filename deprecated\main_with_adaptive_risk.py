# cta_mvp/main_with_adaptive_risk.py

import json
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.adaptive_risk_backtest import run_backtest_with_adaptive_risk
from utils.plot import plot_equity_curve_with_signals
from utils.plot_pnl import plot_trade_pnl
from execution.adaptive_risk_executor import simulate_execution_with_adaptive_risk

if __name__ == "__main__":
    # 从配置文件读取参数
    with open("config/strategy_config.json", encoding="utf-8") as f:
        strategy_config = json.load(f)
        
    with open("config/adaptive_risk_config.json", encoding="utf-8") as f:
        risk_config = json.load(f)

    # 通过策略工厂加载策略
    strategy = StrategyFactory.create(strategy_config)
    strategy.validate_config()

    symbol = strategy_config["symbol"]
    timeframe = strategy_config["timeframe"]
    limit = strategy_config.get("limit", 500)

    print(f"获取 {symbol} 的 {timeframe} 数据，共 {limit} 条...")
    df = fetch_ohlcv(symbol, timeframe, limit=limit)

    # 生成信号
    signals = strategy.generate_signals(df)

    # 带自适应风控的回测
    print("\n执行带自适应风控的回测...")
    df_result, trade_log = run_backtest_with_adaptive_risk(df, signals, risk_config)
    
    # 打印回测结果
    total_return = (df_result["equity"].iloc[-1] - 1) * 100
    print(f"回测结果: 总收益率 {total_return:.2f}%")
    
    # 计算胜率
    if trade_log:
        win_trades = [t for t in trade_log if t["pnl"] > 0]
        win_rate = len(win_trades) / len(trade_log) * 100
        print(f"交易次数: {len(trade_log)}, 胜率: {win_rate:.2f}%")
        
        # 计算平均盈亏比
        if win_trades and len(trade_log) > len(win_trades):
            avg_win = sum(t["pnl"] for t in win_trades) / len(win_trades)
            avg_loss = sum(t["pnl"] for t in trade_log if t["pnl"] <= 0) / (len(trade_log) - len(win_trades))
            profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
            print(f"平均盈亏比: {profit_loss_ratio:.2f}")
    
    # 模拟执行
    print("\n执行带自适应风控的模拟交易...")
    simulate_execution_with_adaptive_risk(df_result, risk_config)
    
    # 绘制图表
    plot_equity_curve_with_signals(df_result)
    plot_trade_pnl("trade_log.csv")
