# risk_management/base.py

from typing import Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np

class RiskManager:
    """
    风控管理基类，提供风控相关的基础功能
    """
    def __init__(self, config: Dict):
        """
        初始化风控管理器

        :param config: 风控配置字典
        """
        self.config = config
        self.initial_capital = config.get("initial_capital", 10000.0)  # 初始资金
        self.current_capital = self.initial_capital  # 当前资金
        self.positions = {}  # 当前持仓 {symbol: {"size": 数量, "entry_price": 入场价格, "entry_time": 入场时间}}
        self.trade_history = []  # 交易历史

    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                          price: float, signal: int, symbol: str) -> bool:
        """
        检查是否可以入场

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许入场
        """
        # 基类默认允许所有入场信号
        return True

    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                         price: float, signal: int, symbol: str) -> bool:
        """
        检查是否可以出场

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许出场
        """
        # 基类默认允许所有出场信号
        return True

    def calculate_position_size(self, df: pd.DataFrame, time: pd.Timestamp,
                               price: float, symbol: str) -> float:
        """
        计算仓位大小

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param symbol: 交易对
        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        # 基类默认使用固定比例
        position_ratio = self.config.get("position_ratio", 1.0)
        return position_ratio

    def on_trade_completed(self, entry_time: pd.Timestamp, entry_price: float,
                          exit_time: pd.Timestamp, exit_price: float,
                          pnl: float, symbol: str) -> None:
        """
        交易完成后的回调

        :param entry_time: 入场时间
        :param entry_price: 入场价格
        :param exit_time: 出场时间
        :param exit_price: 出场价格
        :param pnl: 盈亏金额
        :param symbol: 交易对
        """
        # 更新资金
        self.current_capital += pnl

        # 记录交易历史
        self.trade_history.append({
            "entry_time": entry_time,
            "entry_price": entry_price,
            "exit_time": exit_time,
            "exit_price": exit_price,
            "pnl": pnl,
            "symbol": symbol,
            "capital_after_trade": self.current_capital
        })

        # 清理持仓状态
        self.remove_position(symbol)

    def get_trade_history(self) -> List[Dict]:
        """
        获取交易历史

        :return: 交易历史列表
        """
        return self.trade_history

    def get_current_capital(self) -> float:
        """
        获取当前资金

        :return: 当前资金
        """
        return self.current_capital

    def get_current_positions(self) -> Dict:
        """
        获取当前持仓

        :return: 当前持仓字典
        """
        return self.positions

    def add_position(self, symbol: str, size: float, entry_price: float,
                    entry_time: pd.Timestamp) -> None:
        """
        添加持仓

        :param symbol: 交易对
        :param size: 仓位大小
        :param entry_price: 入场价格
        :param entry_time: 入场时间
        """
        self.positions[symbol] = {
            "size": size,
            "entry_price": entry_price,
            "entry_time": entry_time
        }

    def remove_position(self, symbol: str) -> None:
        """
        移除持仓

        :param symbol: 交易对
        """
        if symbol in self.positions:
            del self.positions[symbol]

    def get_position(self, symbol: str) -> Optional[Dict]:
        """
        获取指定交易对的持仓信息

        :param symbol: 交易对
        :return: 持仓信息字典，如果不存在则返回None
        """
        return self.positions.get(symbol)

    def has_position(self, symbol: str) -> bool:
        """
        检查是否持有指定交易对的仓位

        :param symbol: 交易对
        :return: 是否持有仓位
        """
        return symbol in self.positions

    def get_position_count(self) -> int:
        """
        获取当前持仓数量

        :return: 持仓数量
        """
        return len(self.positions)
