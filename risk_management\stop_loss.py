# risk_management/stop_loss.py

from typing import Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from risk_management.base import RiskManager

class StopLossManager(RiskManager):
    """
    止损止盈风控管理器
    """
    def __init__(self, config: Dict):
        """
        初始化止损止盈风控管理器

        :param config: 风控配置字典，可包含以下参数：
            - stop_loss_pct: 固定止损百分比，默认为0.05（5%）
            - take_profit_pct: 固定止盈百分比，默认为0.1（10%）
            - trailing_stop_pct: 跟踪止损百分比，默认为None（不启用）
            - max_loss_per_trade: 单笔交易最大亏损金额，默认为None（不限制）
            - max_drawdown_pct: 最大回撤百分比，默认为0.2（20%）
            - min_holding_periods: 最小持仓周期数，默认为5（至少持有5个周期）
        """
        super().__init__(config)
        self.stop_loss_pct = config.get("stop_loss_pct", 0.05)
        self.take_profit_pct = config.get("take_profit_pct", 0.1)
        self.trailing_stop_pct = config.get("trailing_stop_pct")
        self.max_loss_per_trade = config.get("max_loss_per_trade")
        self.max_drawdown_pct = config.get("max_drawdown_pct", 0.2)
        self.min_holding_periods = config.get("min_holding_periods", 5)

        # 是否启用最小持仓时间限制
        self.enable_min_holding = config.get("enable_min_holding", True)

        # 打印风控参数 - 安全的字符串格式化
        stop_loss_str = f"{self.stop_loss_pct:.4f}" if self.stop_loss_pct is not None else "N/A"
        take_profit_str = f"{self.take_profit_pct:.4f}" if self.take_profit_pct is not None else "N/A"
        trailing_stop_str = f"{self.trailing_stop_pct:.4f}" if self.trailing_stop_pct is not None else "N/A"
        max_loss_str = str(self.max_loss_per_trade) if self.max_loss_per_trade is not None else "N/A"
        max_drawdown_str = f"{self.max_drawdown_pct:.4f}" if self.max_drawdown_pct is not None else "N/A"

        print(f"初始化止损止盈管理器: 止损={stop_loss_str}, 止盈={take_profit_str}, 跟踪止损={trailing_stop_str}, 单笔最大亏损={max_loss_str}, 最大回撤={max_drawdown_str}")
        print(f"最小持仓周期: {self.min_holding_periods}, 启用最小持仓: {self.enable_min_holding}")

        # 跟踪止损的最高价记录
        self.highest_prices = {}

        # 记录持仓周期计数
        self.holding_periods = {}

    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                          price: float, signal: int, symbol: str) -> bool:
        """
        检查是否可以入场，考虑最大回撤限制

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许入场
        """
        # 检查最大回撤限制
        if self.max_drawdown_pct is not None:
            # 计算当前回撤
            drawdown = 1 - self.current_capital / max(self.initial_capital, max([t["capital_after_trade"] for t in self.trade_history]) if self.trade_history else self.initial_capital)
            if drawdown >= self.max_drawdown_pct:
                print(f"[{time}] ⚠️ 达到最大回撤限制 {drawdown:.2%}，拒绝入场信号")
                return False

        return True

    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                         price: float, signal: int, symbol: str) -> (bool, str):
        """
        检查是否需要出场，考虑止损止盈条件
        返回：(是否允许出场, 原因字符串)
        """
        if not self.has_position(symbol):
            return False, "无持仓"

        position = self.get_position(symbol)
        entry_price = position["entry_price"]
        entry_time = position["entry_time"]
        entry_index = df.index.get_loc(entry_time)
        current_index = df.index.get_loc(time)
        holding_periods = current_index - entry_index
        self.holding_periods[symbol] = holding_periods
        pnl_pct = (price - entry_price) / entry_price

        if self.enable_min_holding and self.holding_periods[symbol] < self.min_holding_periods:
            if pnl_pct <= -self.stop_loss_pct:
                return True, "持仓时间不足但触发止损"
            else:
                return False, f"持仓时间不足（{self.holding_periods[symbol]}/{self.min_holding_periods}），未触发止损"

        if self.stop_loss_pct is not None and pnl_pct <= -self.stop_loss_pct:
            return True, "触发固定止损"
        if self.take_profit_pct is not None and pnl_pct >= self.take_profit_pct:
            return True, "触发固定止盈"
        if self.trailing_stop_pct is not None:
            if symbol not in self.highest_prices or price > self.highest_prices[symbol]:
                self.highest_prices[symbol] = price
            drawdown_from_high = (self.highest_prices[symbol] - price) / self.highest_prices[symbol]
            if drawdown_from_high >= self.trailing_stop_pct:
                return True, "触发跟踪止损"
        if self.max_loss_per_trade is not None:
            position_size = position["size"]
            current_loss = position_size * pnl_pct  # 实际亏损金额
            if current_loss <= -self.max_loss_per_trade:
                return True, "触发单笔最大亏损"
        return False, "未满足任何出场条件"

    def on_trade_completed(self, entry_time: pd.Timestamp, entry_price: float,
                          exit_time: pd.Timestamp, exit_price: float,
                          pnl: float, symbol: str) -> None:
        """
        交易完成后的回调，清理跟踪止损的最高价记录和持仓周期计数

        :param entry_time: 入场时间
        :param entry_price: 入场价格
        :param exit_time: 出场时间
        :param exit_price: 出场价格
        :param pnl: 盈亏金额
        :param symbol: 交易对
        """
        super().on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, symbol)

        # 清理跟踪止损的最高价记录
        if symbol in self.highest_prices:
            del self.highest_prices[symbol]

        # 清理持仓周期计数
        if symbol in self.holding_periods:
            del self.holding_periods[symbol]
