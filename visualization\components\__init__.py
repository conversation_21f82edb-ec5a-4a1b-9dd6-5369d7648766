# visualization/components/__init__.py
"""
可视化组件包
"""

from .charts import (
    plot_candlestick_with_signals,
    plot_equity_curve,
    plot_multi_equity_curves,
    plot_drawdown_comparison,
    plot_trade_pnl_distribution
)

from .metrics import (
    display_metric_cards,
    display_performance_table,
    display_strategy_ranking,
    display_detailed_metrics,
    format_percentage,
    format_number
)

from .forms import (
    create_strategy_selector,
    create_parameter_form,
    create_multi_strategy_selector
)

__all__ = [
    # Charts
    'plot_candlestick_with_signals',
    'plot_equity_curve',
    'plot_multi_equity_curves',
    'plot_drawdown_comparison',
    'plot_trade_pnl_distribution',

    # Metrics
    'display_metric_cards',
    'display_performance_table',
    'display_strategy_ranking',
    'display_detailed_metrics',
    'format_percentage',
    'format_number',

    # Forms
    'create_strategy_selector',
    'create_parameter_form',
    'create_multi_strategy_selector'
]
