# visualization/pages/parameter_config.py
"""
参数配置页面
"""

import streamlit as st
import pandas as pd
import json
import os

from ..components.forms import (
    create_parameter_form,
    create_risk_parameter_form,
    create_data_source_form,
    create_export_options
)

def show_parameter_config():
    """参数配置页面"""
    st.header("⚙️ 参数配置管理")

    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs([
        "📊 回测参数",
        "🛡️ 风控参数",
        "📈 数据源配置",
        "💾 配置管理"
    ])

    with tab1:
        _show_backtest_config()

    with tab2:
        _show_risk_config()

    with tab3:
        _show_data_source_config()

    with tab4:
        _show_config_management()

def _show_backtest_config():
    """显示回测参数配置"""
    st.subheader("📊 回测参数配置")

    # 回测参数表单
    params = create_parameter_form()

    # 保存配置按钮
    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 保存回测配置", type="primary"):
            _save_config("backtest_params", params)
            st.success("回测参数配置已保存")

    with col2:
        if st.button("🔄 重置为默认值"):
            st.rerun()

    # 显示当前配置
    with st.expander("🔍 当前配置详情"):
        st.json(params)

def _show_risk_config():
    """显示风控参数配置"""
    st.subheader("🛡️ 风控参数配置")

    # 风控参数表单
    risk_params = create_risk_parameter_form()

    # 保存配置按钮
    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 保存风控配置", type="primary"):
            _save_config("risk_params", risk_params)
            st.success("风控参数配置已保存")

    with col2:
        if st.button("🔄 重置风控配置"):
            st.rerun()

    # 显示当前配置
    with st.expander("🔍 当前风控配置"):
        st.json(risk_params)

def _show_data_source_config():
    """显示数据源配置"""
    st.subheader("📈 数据源配置")

    # 数据源参数表单
    data_params = create_data_source_form()

    # 导出选项
    export_options = create_export_options()

    # 保存配置按钮
    col1, col2 = st.columns(2)

    with col1:
        if st.button("💾 保存数据源配置", type="primary"):
            config = {**data_params, **export_options}
            _save_config("data_source_params", config)
            st.success("数据源配置已保存")

    with col2:
        if st.button("🔄 重置数据源配置"):
            st.rerun()

def _show_config_management():
    """显示配置管理"""
    st.subheader("💾 配置文件管理")

    # 配置文件列表
    config_dir = "config"
    if os.path.exists(config_dir):
        config_files = [f for f in os.listdir(config_dir) if f.endswith('.json')]

        if config_files:
            st.subheader("📁 现有配置文件")

            for config_file in config_files:
                with st.expander(f"📄 {config_file}"):
                    file_path = os.path.join(config_dir, config_file)

                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            config_data = json.load(f)

                        # 显示配置内容
                        st.json(config_data)

                        # 操作按钮
                        col1, col2, col3 = st.columns(3)

                        with col1:
                            if st.button(f"📝 编辑 {config_file}", key=f"edit_{config_file}"):
                                st.info("编辑功能开发中...")

                        with col2:
                            if st.button(f"📋 复制 {config_file}", key=f"copy_{config_file}"):
                                st.info("复制功能开发中...")

                        with col3:
                            if st.button(f"🗑️ 删除 {config_file}", key=f"delete_{config_file}"):
                                st.warning("删除功能开发中...")

                    except Exception as e:
                        st.error(f"读取配置文件失败: {e}")
        else:
            st.info("暂无配置文件")
    else:
        st.warning("配置目录不存在")

    # 新建配置文件
    st.subheader("➕ 新建配置文件")

    with st.form("new_config_form"):
        config_name = st.text_input("配置文件名", placeholder="例如: my_strategy_config")
        config_type = st.selectbox(
            "配置类型",
            ["策略配置", "回测配置", "风控配置", "自定义配置"]
        )

        if st.form_submit_button("创建配置文件"):
            if config_name:
                _create_new_config(config_name, config_type)
            else:
                st.error("请输入配置文件名")

def _save_config(config_type: str, config_data: dict):
    """
    保存配置到文件

    Args:
        config_type: 配置类型
        config_data: 配置数据
    """
    try:
        config_dir = "config"
        os.makedirs(config_dir, exist_ok=True)

        file_path = os.path.join(config_dir, f"{config_type}.json")

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)

        return True
    except Exception as e:
        st.error(f"保存配置失败: {e}")
        return False

def _create_new_config(config_name: str, config_type: str):
    """
    创建新的配置文件

    Args:
        config_name: 配置文件名
        config_type: 配置类型
    """
    try:
        # 根据配置类型创建模板
        if config_type == "策略配置":
            template = {
                "strategy_type": "dual_ma",
                "symbol": "BTCUSDT",
                "timeframe": "1h",
                "short_window": 10,
                "long_window": 30,
                "display_name": config_name
            }
        elif config_type == "回测配置":
            template = {
                "limit": 500,
                "initial_capital": 10000,
                "fee": 0.001,
                "slippage": 0.0005
            }
        elif config_type == "风控配置":
            template = {
                "stop_loss_enabled": True,
                "stop_loss_pct": 0.05,
                "take_profit_enabled": False,
                "take_profit_pct": 0.10,
                "max_position_size": 1.0
            }
        else:
            template = {
                "name": config_name,
                "type": config_type,
                "created_at": str(pd.Timestamp.now())
            }

        # 保存配置文件
        config_dir = "config"
        os.makedirs(config_dir, exist_ok=True)

        file_path = os.path.join(config_dir, f"{config_name}.json")

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(template, f, ensure_ascii=False, indent=2)

        st.success(f"配置文件 {config_name}.json 创建成功")

    except Exception as e:
        st.error(f"创建配置文件失败: {e}")
