# risk_management/adaptive_risk_manager.py

from typing import Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from risk_management.base import RiskManager

class AdaptiveRiskManager(RiskManager):
    """
    自适应风控管理器，根据市场趋势动态调整风控参数
    """
    def __init__(self, config: Dict):
        """
        初始化自适应风控管理器

        :param config: 风控配置字典，可包含以下参数：
            - initial_capital: 初始资金，默认为10000.0
            - position_ratio: 仓位比例，默认为1.0（100%）
            - stop_loss_pct: 止损百分比，默认为0.1（10%）
            - take_profit_pct: 止盈百分比，默认为0.2（20%）
            - trailing_stop_pct: 跟踪止损百分比，默认为0.05（5%）
            - trend_window: 趋势判断窗口期，默认为20
            - min_holding_periods: 最小持仓周期数，默认为10
            - enable_trend_following: 是否启用趋势跟踪模式，默认为True
        """
        super().__init__(config)
        self.stop_loss_pct = config.get("stop_loss_pct", 0.1)
        self.take_profit_pct = config.get("take_profit_pct", 0.2)
        self.trailing_stop_pct = config.get("trailing_stop_pct", 0.05)
        self.trend_window = config.get("trend_window", 20)
        self.min_holding_periods = config.get("min_holding_periods", 10)
        self.enable_trend_following = config.get("enable_trend_following", True)

        # 记录持仓周期计数
        self.holding_periods = {}

        # 跟踪止损的最高价记录
        self.highest_prices = {}

        # 打印初始化信息
        print(f"初始化自适应风控管理器: stop_loss_pct={self.stop_loss_pct}, take_profit_pct={self.take_profit_pct}, trailing_stop_pct={self.trailing_stop_pct}")

    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                          price: float, signal: int, symbol: str) -> bool:
        """
        检查是否可以入场，考虑市场趋势

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许入场
        """
        # 基础检查
        position_count = self.get_position_count()
        print(f"[{time}] 📊 当前持仓数量: {position_count}")
        if position_count >= 1:
            print(f"[{time}] ⚠️ 已有持仓，拒绝入场")
            return False

        # 如果是第一次交易，直接允许入场
        if not hasattr(self, 'trade_history') or not self.trade_history:
            print(f"[{time}] 🆕 第一次交易，允许入场")
            return True

        # 获取上一次交易的退出时间和价格
        last_trade = self.trade_history[-1]
        last_exit_time = last_trade["exit_time"]
        last_exit_price = last_trade["exit_price"]

        # 计算距离上次交易的时间间隔（以分钟为单位）
        time_diff = (time - last_exit_time).total_seconds() / 60

        # 打印调试信息
        print(f"[{time}] 🕒 距离上次交易时间: {time_diff:.1f}分钟")

        # 如果距离上次交易时间太短，拒绝入场
        if time_diff < 5:  # 至少等待5分钟
            print(f"[{time}] ⏱️ 时间间隔太短，拒绝入场")
            return False

        # 如果距离上次交易时间超过30分钟，允许入场
        if time_diff > 30:
            print(f"[{time}] ⏰ 距离上次交易时间超过30分钟，允许入场")
            return True

        # 如果上次交易是亏损的，需要更谨慎
        if last_trade["pnl"] < 0:
            # 计算当前价格与上次退出价格的变化
            price_change = (price - last_exit_price) / last_exit_price

            # 打印调试信息
            print(f"[{time}] 📊 价格变化: {price_change:.2%}, 上次价格: {last_exit_price:.2f}, 当前价格: {price:.2f}")

            # 如果价格没有明显反弹，拒绝入场
            if price_change < 0.0005:  # 至少需要0.05%的反弹
                print(f"[{time}] 📉 价格反弹不足，拒绝入场")
                return False

            # 打印调试信息
            print(f"[{time}] ℹ️ 价格反弹 {price_change:.2%}，允许入场")
        else:
            print(f"[{time}] 💰 上次交易盈利，允许入场")

        return True

    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                         price: float, signal: int, symbol: str) -> bool:
        """
        检查是否需要出场，考虑市场趋势和止损止盈条件

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否需要出场
        """
        # 如果没有持仓，不需要检查出场
        if not self.has_position(symbol):
            return False

        position = self.get_position(symbol)
        entry_price = position["entry_price"]

        # 初始化持仓周期计数（如果是新入场）
        if symbol not in self.holding_periods:
            self.holding_periods[symbol] = 0

        # 增加持仓周期计数
        self.holding_periods[symbol] += 1

        # 计算当前盈亏百分比
        pnl_pct = (price - entry_price) / entry_price

        # 判断市场趋势
        is_trend_following = self._is_trend_following(df, price, entry_price)

        # 如果是趋势跟踪模式且趋势良好，放宽止损止盈条件
        if self.enable_trend_following and is_trend_following:
            # 趋势良好时，只有在大幅回撤时才止损
            if pnl_pct <= -self.stop_loss_pct * 1.5:
                print(f"[{time}] 🛑 趋势良好但触发扩大止损，亏损: {pnl_pct:.2%}")
                return True

            # 趋势良好时，提高止盈目标
            if pnl_pct >= self.take_profit_pct * 1.5:
                print(f"[{time}] 💰 趋势良好且触发扩大止盈，盈利: {pnl_pct:.2%}")
                return True

            # 趋势良好时，使用更宽松的跟踪止损
            if self._check_trailing_stop(price, symbol, self.trailing_stop_pct * 1.5):
                print(f"[{time}] 📉 趋势良好但触发扩大跟踪止损")
                return True
        else:
            # 非趋势跟踪模式或趋势不佳时，使用标准止损止盈条件

            # 检查最小持仓周期
            if self.holding_periods[symbol] < self.min_holding_periods:
                # 如果持仓时间不足，只有在触发止损时才允许出场
                if pnl_pct <= -self.stop_loss_pct:
                    print(f"[{time}] 🛑 触发固定止损，亏损: {pnl_pct:.2%}")
                    return True
                return False

            # 固定止损检查
            if pnl_pct <= -self.stop_loss_pct:
                print(f"[{time}] 🛑 触发固定止损，亏损: {pnl_pct:.2%}")
                return True

            # 固定止盈检查
            if pnl_pct >= self.take_profit_pct:
                print(f"[{time}] 💰 触发固定止盈，盈利: {pnl_pct:.2%}")
                return True

            # 跟踪止损检查
            if self._check_trailing_stop(price, symbol, self.trailing_stop_pct):
                print(f"[{time}] 📉 触发跟踪止损")
                return True

        return False

    def _is_trend_following(self, df: pd.DataFrame, current_price: float, entry_price: float) -> bool:
        """
        判断当前是否处于趋势跟踪模式

        :param df: 行情数据
        :param current_price: 当前价格
        :param entry_price: 入场价格
        :return: 是否处于趋势跟踪模式
        """
        # 如果没有启用趋势跟踪，直接返回False
        if not self.enable_trend_following:
            return False

        # 如果数据不足，无法判断趋势
        if len(df) < self.trend_window:
            return False

        # 计算短期均线和长期均线
        short_ma = df["close"].tail(10).mean()
        long_ma = df["close"].tail(self.trend_window).mean()

        # 判断趋势方向
        trend_up = short_ma > long_ma

        # 判断当前价格相对于入场价格的变化
        price_up = current_price > entry_price

        # 趋势方向与价格变化方向一致，认为是趋势跟踪
        return trend_up == price_up

    def _check_trailing_stop(self, price: float, symbol: str, trailing_pct: float) -> bool:
        """
        检查是否触发跟踪止损

        :param price: 当前价格
        :param symbol: 交易对
        :param trailing_pct: 跟踪止损百分比
        :return: 是否触发跟踪止损
        """
        position = self.get_position(symbol)
        entry_price = position["entry_price"]

        # 更新最高价
        if symbol not in self.highest_prices or price > self.highest_prices[symbol]:
            self.highest_prices[symbol] = price

        # 只有在盈利的情况下才启用跟踪止损
        if price <= entry_price:
            return False

        # 计算从最高价的回撤
        drawdown_from_high = (self.highest_prices[symbol] - price) / self.highest_prices[symbol]

        # 判断是否触发跟踪止损
        return drawdown_from_high >= trailing_pct

    def on_trade_completed(self, entry_time: pd.Timestamp, entry_price: float,
                          exit_time: pd.Timestamp, exit_price: float,
                          pnl: float, symbol: str) -> None:
        """
        交易完成后的回调，清理跟踪止损的最高价记录和持仓周期计数

        :param entry_time: 入场时间
        :param entry_price: 入场价格
        :param exit_time: 出场时间
        :param exit_price: 出场价格
        :param pnl: 盈亏金额
        :param symbol: 交易对
        """
        super().on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, symbol)

        # 清理跟踪止损的最高价记录
        if symbol in self.highest_prices:
            del self.highest_prices[symbol]

        # 清理持仓周期计数
        if symbol in self.holding_periods:
            del self.holding_periods[symbol]
