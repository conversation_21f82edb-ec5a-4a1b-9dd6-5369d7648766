from abc import ABC, abstractmethod
import pandas as pd

class IStrategy(ABC):
    """策略接口规范，定义策略必须实现的核心方法"""
    
    def __init__(self, config):
        """
        初始化策略
        
        :param config: 策略配置字典
        """
        self.config = config
    
    @abstractmethod
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        根据输入数据生成交易信号
        
        :param df: 原始价格数据DataFrame
        :return: 包含信号的DataFrame
        """
        pass
    
    @abstractmethod
    def validate_config(self) -> None:
        """
        验证策略配置有效性
        
        :raises ValueError: 配置验证失败时抛出异常
        """
        pass
```

## 2. 数据访问接口 (IDataSource)
```python
from abc import ABC, abstractmethod
import pandas as pd
from typing import Optional

class IDataSource(ABC):
    """数据访问接口，定义市场数据获取的标准方法"""
    
    @abstractmethod
    def fetch_data(self, symbol: str, timeframe: str, limit: int = 500) -> pd.DataFrame:
        """
        获取市场历史数据
        
        :param symbol: 交易标的（如BTC/USDT）
        :param timeframe: 时间周期（如1h、1d）
        :param limit: 要获取的数据条数
        :return: 包含OHLCV数据的DataFrame
        """
        pass
```

## 3. 风控服务接口 (IRiskManagement)
```python
from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, List, Optional, Union, Tuple

class IRiskManagement(ABC):
    """风控服务接口，定义风控模块必须实现的核心方法"""
    
    def __init__(self, config: Dict):
        """
        初始化风控管理器
        
        :param config: 风控配置字典
        """
        pass
    
    @abstractmethod
    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp, 
                         price: float, signal: int, symbol: str) -> bool:
        """
        检查是否允许入场
        
        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许入场
        """
        pass
    
    @abstractmethod
    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp, 
                        price: float, signal: int, symbol: str) -> bool:
        """
        检查是否需要出场
        
        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否需要出场
        """
        pass
    
    @abstractmethod
    def calculate_position_size(self, df: pd.DataFrame, time: pd.Timestamp, 
                           price: float, symbol: str) -> float:
        """
        计算仓位大小
        
        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param symbol: 交易对
        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        pass
```

## 4. 策略注册中心接口 (IStrategyRegistry)
```python
from abc import ABC, abstractmethod
from typing import Type

class IStrategyRegistry(ABC):
    """策略注册中心接口，定义策略注册和获取的标准方法"""
    
    @abstractmethod
    def register_strategy(self, name: str, strategy_class: Type) -> None:
        """
        注册策略类型
        
        :param name: 策略名称
        :param strategy_class: 策略类
        """
        pass
    
    @abstractmethod
    def get_strategy(self, name: str) -> Type:
        """
        获取已注册的策略类
        
        :param name: 策略名称
        :return: 策略类
        """
        pass
```

## 接口实现建议

### 策略接口实现示例
```python
class DualMAStrategy(IStrategy):
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        # 实现双均线信号生成逻辑
        pass
    
    def validate_config(self) -> None:
        # 实现配置验证逻辑
        pass
```

### 数据源接口实现示例
```python
class BinanceDataSource(IDataSource):
    def fetch_data(self, symbol: str, timeframe: str, limit: int = 500) -> pd.DataFrame:
        # 实现从Binance获取数据的逻辑
        pass
```

### 风控接口实现示例
```python
class RiskManager(IRiskManagement):
    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp, 
                         price: float, signal: int, symbol: str) -> bool:
        # 实现入场检查逻辑
        pass
    
    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp, 
                        price: float, signal: int, symbol: str) -> bool:
        # 实现出场检查逻辑
        pass
    
    def calculate_position_size(self, df: pd.DataFrame, time: pd.Timestamp, 
                           price: float, symbol: str) -> float:
        # 实现仓位计算逻辑
        pass
```

## 接口使用说明

### 策略工厂重构示例
```python
class StrategyRegistry(IStrategyRegistry):
    def __init__(self):
        self._strategies = {}
    
    def register_strategy(self, name: str, strategy_class: Type) -> None:
        self._strategies[name] = strategy_class
    
    def get_strategy(self, name: str) -> Type:
        if name not in self._strategies:
            raise ValueError(f"未知策略名称: {name}")
        return self._strategies[name]
```

### 主程序使用示例
```python
# 初始化策略注册中心
strategy_registry = StrategyRegistry()
strategy_registry.register_strategy("dual_ma", DualMAStrategy)
strategy_registry.register_strategy("boll_breakout", BollBreakoutStrategy)

# 创建策略实例
strategy_class = strategy_registry.get_strategy("dual_ma")
strategy = strategy_class(config)

# 使用数据源接口
binance_source = BinanceDataSource()
df = binance_source.fetch_data("BTC/USDT", "1h", 500)

# 使用风控接口
risk_manager = RiskManager(risk_config)
signals = strategy.generate_signals(df)

for time, row in df.iterrows():
    # 检查入场信号
    if risk_manager.check_entry_signal(df, time, row["close"], signals.loc[time, "signal"], "BTC/USDT"):
        # 执行入场逻辑
        pass

# 使用信号生成交易
position = signals["position"]
```

## 接口验证要求
1. 所有实现必须包含完整文档字符串
2. 参数类型必须严格遵循接口定义
3. 错误处理必须保持一致性
4. 必须通过接口规范的单元测试
5. 接口实现必须记录完整的变更日志

## 接口兼容性保障
1. 接口版本号必须显式声明
2. 向后兼容的修改需要提供适配层
3. 接口变更必须经过代码评审
4. 必须维护接口变更历史记录
5. 接口实现必须保持单一职责原则