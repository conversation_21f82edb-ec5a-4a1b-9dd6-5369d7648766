# strategy/base.py

class BaseStrategy:
    def __init__(self, config):
        self.config = config

    def generate_signals(self, df):
        raise NotImplementedError("必须由子类实现")

class StrategyFactory:
    @staticmethod
    def create(config):
        strategy_name = config.get("strategy_name")
        if strategy_name == "dual_ma":
            from strategy.dual_ma import DualMAStrategy
            return DualMAStrategy(config)
        elif strategy_name == "boll_breakout":
            from strategy.boll_breakout import BollBreakoutStrategy
            return BollBreakoutStrategy(config)
        else:
            raise ValueError(f"未知策略名称: {strategy_name}")

class StrategyConfigError(Exception):
    """
    策略参数配置异常。
    用于策略参数校验失败时抛出，便于上层统一捕获。
    """
    pass
