# risk_management/position_sizing.py

from typing import Dict, List, Optional, Union, Tuple
import pandas as pd
import numpy as np
from risk_management.base import RiskManager
import logging

class PositionSizeManager(RiskManager):
    """
    仓位管理风控管理器
    """
    def __init__(self, config: Dict):
        """
        初始化仓位管理风控管理器

        :param config: 风控配置字典，可包含以下参数：
            - position_sizing_method: 仓位管理方法，可选值为"fixed_ratio"（固定比例）、"kelly"（凯利公式）、"volatility"（波动率调整），默认为"fixed_ratio"
            - position_ratio: 固定仓位比例，默认为1.0（100%）
            - max_positions: 最大持仓数量，默认为1
            - kelly_fraction: 凯利公式分数，默认为0.5（半凯利）
            - volatility_lookback: 波动率计算回溯期，默认为20
            - volatility_target: 目标波动率，默认为0.01（1%）
        """
        super().__init__(config)
        self.position_sizing_method = config.get("position_sizing_method", "fixed_ratio")
        self.position_ratio = config.get("position_ratio", 1.0)
        self.max_positions = config.get("max_positions", 1)
        self.kelly_fraction = config.get("kelly_fraction", 0.5)
        self.volatility_lookback = config.get("volatility_lookback", 20)
        self.volatility_target = config.get("volatility_target", 0.01)

    def check_entry_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                          price: float, signal: int, symbol: str) -> bool:
        """
        检查是否可以入场，考虑最大持仓数量限制

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param signal: 信号值
        :param symbol: 交易对
        :return: 是否允许入场
        """
        # 检查最大持仓数量限制
        if self.get_position_count() >= self.max_positions:
            print(f"[{time}] ⚠️ 达到最大持仓数量限制 {self.max_positions}，拒绝入场信号")
            return False

        return True

    def check_exit_signal(self, df: pd.DataFrame, time: pd.Timestamp,
                         price: float, signal: int, symbol: str) -> (bool, str):
        """
        检查是否需要出场，仓位管理器默认不触发出场
        返回：(是否需要出场, 原因字符串)
        """
        # 仓位管理器默认不触发出场
        logging.debug(f"[{time}] 仓位管理器默认不触发出场，symbol={symbol}")
        return False, "仓位管理器默认不触发出场"

    def calculate_position_size(self, df: pd.DataFrame, time: pd.Timestamp,
                               price: float, symbol: str) -> float:
        """
        计算仓位大小

        :param df: 行情数据
        :param time: 当前时间
        :param price: 当前价格
        :param symbol: 交易对
        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        if self.position_sizing_method == "fixed_ratio":
            # 固定比例仓位
            return self._calculate_fixed_ratio_position()
        elif self.position_sizing_method == "kelly":
            # 凯利公式仓位
            return self._calculate_kelly_position(df)
        elif self.position_sizing_method == "volatility":
            # 波动率调整仓位
            return self._calculate_volatility_position(df)
        else:
            # 默认使用固定比例
            return self.position_ratio

    def _calculate_fixed_ratio_position(self) -> float:
        """
        计算固定比例仓位

        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        # 考虑最大持仓数量，平均分配资金
        return self.position_ratio / self.max_positions

    def _calculate_kelly_position(self, df: pd.DataFrame) -> float:
        """
        使用凯利公式计算仓位大小

        :param df: 行情数据
        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        # 如果没有足够的交易历史，使用固定比例
        if len(self.trade_history) < 10:
            return self._calculate_fixed_ratio_position()

        # 计算历史胜率和盈亏比
        wins = [t for t in self.trade_history if t["pnl"] > 0]
        losses = [t for t in self.trade_history if t["pnl"] <= 0]

        if not wins or not losses:
            return self._calculate_fixed_ratio_position()

        win_rate = len(wins) / len(self.trade_history)
        avg_win = sum(t["pnl"] for t in wins) / len(wins)
        avg_loss = abs(sum(t["pnl"] for t in losses) / len(losses))

        # 计算凯利比例
        if avg_loss == 0:
            kelly = 1.0
        else:
            kelly = win_rate - (1 - win_rate) / (avg_win / avg_loss)

        # 使用凯利分数调整（通常使用半凯利）
        kelly = max(0, kelly * self.kelly_fraction)

        # 考虑最大持仓数量，平均分配资金
        return kelly / self.max_positions

    def _calculate_volatility_position(self, df: pd.DataFrame) -> float:
        """
        使用波动率调整计算仓位大小

        :param df: 行情数据
        :return: 仓位大小（单位：资金比例，0-1之间）
        """
        # 计算历史波动率
        if len(df) < self.volatility_lookback:
            return self._calculate_fixed_ratio_position()

        returns = df["close"].pct_change().dropna()
        if len(returns) < self.volatility_lookback:
            return self._calculate_fixed_ratio_position()

        # 计算最近N天的波动率（标准差）
        volatility = returns.tail(self.volatility_lookback).std()

        # 根据目标波动率调整仓位
        if volatility == 0:
            position_size = self.position_ratio
        else:
            position_size = self.volatility_target / volatility

        # 限制仓位大小在合理范围内
        position_size = min(self.position_ratio, max(0.1, position_size))

        # 考虑最大持仓数量，平均分配资金
        return position_size / self.max_positions
