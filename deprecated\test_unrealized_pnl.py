# test_unrealized_pnl.py - 测试未实现盈亏计算

import json
import pandas as pd
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from execution.risk_managed_executor import simulate_execution_with_risk_management
import logging

# 设置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

if __name__ == "__main__":
    # 从配置文件读取参数
    with open("config/strategy_config.json", encoding="utf-8") as f:
        strategy_config = json.load(f)

    with open("config/risk_config.json", encoding="utf-8") as f:
        risk_config = json.load(f)

    # 通过策略工厂加载策略
    strategy = StrategyFactory.create(strategy_config)
    strategy.validate_config()

    symbol = strategy_config["symbol"]
    timeframe = strategy_config["timeframe"]
    limit = 100  # 使用较少的数据来测试

    print(f"\n获取 {symbol} 的 {timeframe} 数据，共 {limit} 条...")
    df = fetch_ohlcv(symbol, timeframe, limit=limit)

    # 生成信号
    signals = strategy.generate_signals(df)

    # 确保signals包含position列（信号的差分）
    if "position" not in signals.columns:
        signals["position"] = signals["signal"].diff().fillna(0)

    print(f"\n策略信号统计:")
    print(f"  买入信号数量: {(signals['position'] == 1).sum()}")
    print(f"  卖出信号数量: {(signals['position'] == -1).sum()}")

    # 显示最后几个价格
    print(f"\n最后5个价格:")
    for i, (time, row) in enumerate(df.tail(5).iterrows()):
        print(f"  {time}: {row['close']:.2f}")

    # 执行模拟交易
    print("\n执行带风控的模拟交易...")
    trade_log = simulate_execution_with_risk_management(signals, risk_config)

    print(f"\n交易详情:")
    for i, trade in enumerate(trade_log):
        print(f"  交易{i+1}: 入场={trade['entry_price']:.2f}, 出场={trade['exit_price']:.2f}, "
              f"盈亏={trade['pnl']:.2f}, 原因={trade['exit_reason']}")
