# visualization/components/forms.py
"""
表单组件模块
"""

import streamlit as st
from typing import Dict, List, Tuple, Any

from ..config import DEFAULT_PARAMS
from ..utils.data_loader import format_strategy_options, get_strategy_display_name

def create_strategy_selector(configs: List[Dict]) -> Tuple[int, Dict]:
    """
    创建策略选择器
    
    Args:
        configs: 策略配置列表
        
    Returns:
        选中的策略索引和配置
    """
    if not configs:
        st.error("未找到策略配置文件，请检查 config/strategies 目录")
        return -1, {}
    
    # 策略选择
    strategy_names = [get_strategy_display_name(config) for config in configs]
    selected_idx = st.selectbox(
        "选择策略", 
        range(len(strategy_names)),
        format_func=lambda x: strategy_names[x]
    )
    
    selected_config = configs[selected_idx]
    
    # 显示策略信息
    st.info(f"**策略类型**: {selected_config.get('strategy_type', 'Unknown')}")
    st.info(f"**交易对**: {selected_config.get('symbol', 'Unknown')}")
    st.info(f"**时间周期**: {selected_config.get('timeframe', 'Unknown')}")
    
    return selected_idx, selected_config

def create_multi_strategy_selector(configs: List[Dict]) -> List[Dict]:
    """
    创建多策略选择器
    
    Args:
        configs: 策略配置列表
        
    Returns:
        选中的策略配置列表
    """
    if not configs:
        st.error("未找到策略配置文件，请检查 config/strategies 目录")
        return []
    
    # 多选策略
    strategy_options = format_strategy_options(configs)
    
    selected_strategies = st.multiselect(
        "选择要比较的策略（可多选）",
        options=list(strategy_options.keys()),
        default=list(strategy_options.keys())[:3] if len(strategy_options) >= 3 else list(strategy_options.keys())
    )
    
    if not selected_strategies:
        st.warning("请至少选择一个策略")
        return []
    
    # 返回选中的配置
    selected_configs = [configs[strategy_options[name]] for name in selected_strategies]
    return selected_configs

def create_parameter_form(config: Dict = None) -> Dict[str, Any]:
    """
    创建参数配置表单
    
    Args:
        config: 策略配置（可选，用于获取默认值）
        
    Returns:
        参数字典
    """
    st.subheader("⚙️ 回测参数")
    
    # 获取默认值
    default_limit = config.get('limit', DEFAULT_PARAMS['limit']) if config else DEFAULT_PARAMS['limit']
    
    # 参数输入
    limit = st.slider(
        "数据条数", 
        100, 2000, 
        default_limit, 
        50,
        help="获取的历史K线数据条数"
    )
    
    initial_capital = st.number_input(
        "初始资金", 
        1000, 100000, 
        DEFAULT_PARAMS['initial_capital'], 
        1000,
        help="回测起始资金（USDT）"
    )
    
    fee = st.slider(
        "手续费率", 
        0.0, 0.01, 
        DEFAULT_PARAMS['fee'], 
        0.0001, 
        format="%.4f",
        help="交易手续费比例"
    )
    
    slippage = st.slider(
        "滑点", 
        0.0, 0.01, 
        DEFAULT_PARAMS['slippage'], 
        0.0001, 
        format="%.4f",
        help="模拟实际交易的价格滑点"
    )
    
    return {
        'limit': limit,
        'initial_capital': initial_capital,
        'fee': fee,
        'slippage': slippage
    }

def create_risk_parameter_form() -> Dict[str, Any]:
    """
    创建风控参数配置表单
    
    Returns:
        风控参数字典
    """
    st.subheader("🛡️ 风控参数")
    
    # 止损参数
    stop_loss_enabled = st.checkbox("启用止损", value=True)
    stop_loss_pct = st.slider(
        "止损比例", 
        0.01, 0.20, 
        0.05, 
        0.01, 
        format="%.2f",
        disabled=not stop_loss_enabled,
        help="单笔交易最大亏损比例"
    )
    
    # 止盈参数
    take_profit_enabled = st.checkbox("启用止盈", value=False)
    take_profit_pct = st.slider(
        "止盈比例", 
        0.01, 0.50, 
        0.10, 
        0.01, 
        format="%.2f",
        disabled=not take_profit_enabled,
        help="单笔交易目标盈利比例"
    )
    
    # 仓位管理
    max_position_size = st.slider(
        "最大仓位比例", 
        0.1, 1.0, 
        1.0, 
        0.1, 
        format="%.1f",
        help="单次交易最大仓位比例"
    )
    
    # 最大回撤限制
    max_drawdown_enabled = st.checkbox("启用最大回撤限制", value=False)
    max_drawdown_pct = st.slider(
        "最大回撤比例", 
        0.05, 0.50, 
        0.20, 
        0.05, 
        format="%.2f",
        disabled=not max_drawdown_enabled,
        help="账户最大回撤比例限制"
    )
    
    return {
        'stop_loss_enabled': stop_loss_enabled,
        'stop_loss_pct': stop_loss_pct,
        'take_profit_enabled': take_profit_enabled,
        'take_profit_pct': take_profit_pct,
        'max_position_size': max_position_size,
        'max_drawdown_enabled': max_drawdown_enabled,
        'max_drawdown_pct': max_drawdown_pct
    }

def create_data_source_form() -> Dict[str, Any]:
    """
    创建数据源配置表单
    
    Returns:
        数据源参数字典
    """
    st.subheader("📊 数据源配置")
    
    # 交易对选择
    symbol = st.selectbox(
        "交易对",
        ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "DOTUSDT"],
        help="选择要分析的交易对"
    )
    
    # 时间周期选择
    timeframe = st.selectbox(
        "时间周期",
        ["1m", "5m", "15m", "30m", "1h", "4h", "1d"],
        index=4,  # 默认选择1h
        help="K线数据的时间周期"
    )
    
    # 数据范围
    col1, col2 = st.columns(2)
    
    with col1:
        start_date = st.date_input(
            "开始日期",
            help="数据获取的开始日期"
        )
    
    with col2:
        end_date = st.date_input(
            "结束日期", 
            help="数据获取的结束日期"
        )
    
    return {
        'symbol': symbol,
        'timeframe': timeframe,
        'start_date': start_date,
        'end_date': end_date
    }

def create_export_options() -> Dict[str, bool]:
    """
    创建导出选项表单
    
    Returns:
        导出选项字典
    """
    st.subheader("📤 导出选项")
    
    export_charts = st.checkbox("导出图表", value=True)
    export_data = st.checkbox("导出数据", value=True)
    export_report = st.checkbox("导出报告", value=False)
    
    if export_report:
        report_format = st.selectbox(
            "报告格式",
            ["PDF", "HTML", "Word"],
            help="选择报告导出格式"
        )
    else:
        report_format = None
    
    return {
        'export_charts': export_charts,
        'export_data': export_data,
        'export_report': export_report,
        'report_format': report_format
    }
