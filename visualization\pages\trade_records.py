# visualization/pages/trade_records.py
"""
交易记录页面
"""

import streamlit as st
import pandas as pd

from ..utils import load_trade_records

def show_trade_records():
    """交易记录页面"""
    st.header("📋 交易记录管理")
    
    # 加载交易记录
    df_trades = load_trade_records()
    
    if df_trades is not None:
        _display_trade_records(df_trades)
    else:
        st.info("暂无交易记录")
        
        # 功能预览
        st.markdown("""
        ### 🎯 计划功能
        
        - **交易记录查看**: 详细的历史交易记录
        - **交易统计分析**: 交易频率、盈亏分布等统计
        - **交易记录筛选**: 按时间、策略、盈亏等条件筛选
        - **交易记录导出**: 支持CSV、Excel等格式导出
        - **交易记录可视化**: 交易时间线、盈亏趋势图等
        """)

def _display_trade_records(df_trades: pd.DataFrame):
    """
    显示交易记录
    
    Args:
        df_trades: 交易记录DataFrame
    """
    # 基本统计信息
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_trades = len(df_trades)
        st.metric("总交易次数", total_trades)
    
    with col2:
        if 'pnl' in df_trades.columns:
            total_pnl = df_trades['pnl'].sum()
            st.metric("总盈亏", f"{total_pnl:.2f} USDT")
    
    with col3:
        if 'pnl' in df_trades.columns:
            win_trades = len(df_trades[df_trades['pnl'] > 0])
            win_rate = win_trades / total_trades if total_trades > 0 else 0
            st.metric("胜率", f"{win_rate:.1%}")
    
    with col4:
        if 'pnl' in df_trades.columns:
            avg_pnl = df_trades['pnl'].mean()
            st.metric("平均盈亏", f"{avg_pnl:.2f} USDT")
    
    # 交易记录表格
    st.subheader("📊 交易记录详情")
    
    # 筛选选项
    with st.expander("🔍 筛选选项"):
        col1, col2 = st.columns(2)
        
        with col1:
            if 'entry_time' in df_trades.columns:
                start_date = st.date_input("开始日期")
                end_date = st.date_input("结束日期")
        
        with col2:
            if 'pnl' in df_trades.columns:
                pnl_filter = st.selectbox(
                    "盈亏筛选",
                    ["全部", "盈利", "亏损"]
                )
    
    # 应用筛选
    filtered_df = df_trades.copy()
    
    # 显示筛选后的记录
    st.dataframe(filtered_df, use_container_width=True)
    
    # 交易分析图表
    if 'pnl' in df_trades.columns:
        st.subheader("📈 交易分析")
        
        tab1, tab2 = st.tabs(["盈亏分布", "时间分析"])
        
        with tab1:
            _plot_pnl_distribution(df_trades)
        
        with tab2:
            _plot_time_analysis(df_trades)

def _plot_pnl_distribution(df_trades: pd.DataFrame):
    """绘制盈亏分布图"""
    import plotly.graph_objects as go
    
    fig = go.Figure()
    
    colors = ['green' if x > 0 else 'red' for x in df_trades['pnl']]
    
    fig.add_trace(
        go.Bar(
            x=list(range(len(df_trades))),
            y=df_trades['pnl'],
            marker_color=colors,
            name='每笔交易盈亏'
        )
    )
    
    fig.update_layout(
        title="交易盈亏分布",
        xaxis_title="交易序号",
        yaxis_title="盈亏(USDT)",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)

def _plot_time_analysis(df_trades: pd.DataFrame):
    """绘制时间分析图"""
    if 'entry_time' not in df_trades.columns:
        st.info("缺少时间数据")
        return
    
    import plotly.graph_objects as go
    
    # 按日期统计交易次数
    df_trades['date'] = pd.to_datetime(df_trades['entry_time']).dt.date
    daily_trades = df_trades.groupby('date').size()
    
    fig = go.Figure()
    
    fig.add_trace(
        go.Scatter(
            x=daily_trades.index,
            y=daily_trades.values,
            mode='lines+markers',
            name='每日交易次数'
        )
    )
    
    fig.update_layout(
        title="每日交易次数趋势",
        xaxis_title="日期",
        yaxis_title="交易次数",
        height=400
    )
    
    st.plotly_chart(fig, use_container_width=True)
