# execution/adaptive_risk_executor.py

import csv
from pathlib import Path
import pandas as pd
from typing import Dict, Optional
from risk_management.adaptive_risk_manager import AdaptiveRisk<PERSON>ana<PERSON>

def simulate_execution_with_adaptive_risk(df, risk_config=None, log_path="trade_log.csv"):
    """
    带自适应风控的模拟交易执行

    :param df: 包含交易信号的DataFrame
    :param risk_config: 风控配置字典，如果为None则使用默认配置
    :param log_path: 交易日志保存路径
    :return: 交易日志列表
    """
    # 默认风控配置
    if risk_config is None:
        risk_config = {
            "initial_capital": 10000.0,
            "position_ratio": 1.0,
            "max_positions": 1,
            "stop_loss_pct": 0.1,
            "take_profit_pct": 0.2,
            "trailing_stop_pct": 0.05,
            "trend_window": 20,
            "min_holding_periods": 10,
            "enable_trend_following": True
        }

    # 创建风控管理器
    risk_manager = AdaptiveRiskManager(risk_config)

    # 打印调试信息
    print(f"调试信息: trade_history={risk_manager.trade_history}")

    position = 0
    entry_price = 0
    entry_time = None
    trade_log = []

    # 检查策略类型
    has_negative_position = (df["position"] < 0).any()

    # 记录初始资金
    initial_capital = risk_manager.initial_capital
    current_capital = initial_capital

    print(f"初始资金: {initial_capital:.2f} USDT")
    print(f"风控设置: 止损={risk_config.get('stop_loss_pct', 0.1):.2%}, "
          f"止盈={risk_config.get('take_profit_pct', 0.2):.2%}, "
          f"跟踪止损={risk_config.get('trailing_stop_pct', 0.05):.2%}, "
          f"趋势跟踪={risk_config.get('enable_trend_following', True)}")

    for time, row in df.iterrows():
        price = row["close"]
        current_position = row["position"]

        # 检查风控触发的出场信号
        if position == 1 and risk_manager.check_exit_signal(df.loc[:time], time, price, current_position, "default"):
            # 风控触发的平仓
            exit_price = price
            exit_time = time

            # 计算盈亏
            position_size = risk_manager.get_position("default")["size"]
            # 计算盈亏百分比，然后乘以仓位大小
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            # 记录交易
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "exit_reason": "risk_management",
                "capital": current_capital
            })

            # 通知风控管理器
            risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")

            # 确保移除持仓
            risk_manager.remove_position("default")

            # 手动添加交易记录到风控管理器的交易历史
            if not hasattr(risk_manager, 'trade_history') or risk_manager.trade_history is None:
                risk_manager.trade_history = []
            risk_manager.trade_history.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "symbol": "default"
            })

            # 打印调试信息
            print(f"[{time}] 📝 记录交易历史: {risk_manager.trade_history}")

            # 更新状态
            position = 0
            print(f"[{time}] 🛑 风控触发卖出 @ {exit_price:.2f}，盈亏: {pnl:.2f} USDT，当前资金: {current_capital:.2f} USDT")

        # 处理策略信号
        elif current_position == 1 and position == 0:
            # 检查风控是否允许入场
            print(f"[{time}] 🔍 检查入场信号: price={price}, current_position={current_position}")
            if risk_manager.check_entry_signal(df.loc[:time], time, price, current_position, "default"):
                # 计算仓位大小
                position_ratio = risk_config.get("position_ratio", 1.0)
                position_size = current_capital * position_ratio

                # 开仓
                entry_price = price
                entry_time = time
                position = 1

                # 记录持仓
                risk_manager.add_position("default", position_size, entry_price, entry_time)

                print(f"[{time}] 💹 买入 @ {entry_price:.2f}，仓位: {position_ratio:.2%}，金额: {position_size:.2f} USDT")
            else:
                print(f"[{time}] ⚠️ 风控拒绝买入信号 @ {price:.2f}")

        # 处理策略卖出信号
        elif (has_negative_position and current_position == -1 and position == 1) or \
             (not has_negative_position and current_position == 0 and position == 1):
            # 平仓
            exit_price = price
            exit_time = time

            # 计算盈亏
            position_size = risk_manager.get_position("default")["size"]
            # 计算盈亏百分比，然后乘以仓位大小
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            # 记录交易
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "exit_reason": "strategy_signal",
                "capital": current_capital
            })

            # 通知风控管理器
            risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")

            # 手动添加交易记录到风控管理器的交易历史
            if not hasattr(risk_manager, 'trade_history') or risk_manager.trade_history is None:
                risk_manager.trade_history = []
            risk_manager.trade_history.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "symbol": "default"
            })

            # 确保移除持仓
            risk_manager.remove_position("default")

            # 打印调试信息
            print(f"[{time}] 📝 记录交易历史: {risk_manager.trade_history}")

            # 更新状态
            position = 0
            print(f"[{time}] 💼 卖出 @ {exit_price:.2f}，盈亏: {pnl:.2f} USDT，当前资金: {current_capital:.2f} USDT")

    if position == 1:
        print("⚠️ 持仓未平仓，模拟盘结束时仍持有头寸")

    # 计算最终收益率
    final_return = (current_capital / initial_capital - 1) * 100
    print(f"\n📊 最终资金: {current_capital:.2f} USDT，收益率: {final_return:.2f}%")

    # 写入CSV文件
    if trade_log:
        log_file = Path(log_path)
        with log_file.open("w", newline="") as f:
            writer = csv.DictWriter(f, fieldnames=trade_log[0].keys())
            writer.writeheader()
            writer.writerows(trade_log)
        print(f"\n✅ 交易日志保存至：{log_path}")

    return trade_log
