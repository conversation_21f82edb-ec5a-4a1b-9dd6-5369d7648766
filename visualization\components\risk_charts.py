# visualization/components/risk_charts.py
"""
风控相关的图表和分析组件
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any

from ..config import get_chart_colors

def display_trade_log_with_risk_info(trade_log: List[Dict]) -> None:
    """
    显示带风控信息的交易记录
    
    Args:
        trade_log: 交易记录列表
    """
    if not trade_log:
        st.info("暂无交易记录")
        return
    
    # 转换为DataFrame
    df_trades = pd.DataFrame(trade_log)
    
    # 格式化时间列
    if 'entry_time' in df_trades.columns:
        df_trades['entry_time'] = pd.to_datetime(df_trades['entry_time']).dt.strftime('%Y-%m-%d %H:%M')
    if 'exit_time' in df_trades.columns:
        df_trades['exit_time'] = pd.to_datetime(df_trades['exit_time']).dt.strftime('%Y-%m-%d %H:%M')
    
    # 重命名列
    column_mapping = {
        'entry_time': '开仓时间',
        'entry_price': '开仓价格',
        'exit_time': '平仓时间',
        'exit_price': '平仓价格',
        'pnl': '盈亏(USDT)',
        'position_size': '仓位大小',
        'capital': '账户资金',
        'exit_reason': '平仓原因'
    }
    
    df_display = df_trades.rename(columns=column_mapping)
    
    # 格式化数值列
    numeric_columns = ['开仓价格', '平仓价格', '盈亏(USDT)', '仓位大小', '账户资金']
    for col in numeric_columns:
        if col in df_display.columns:
            df_display[col] = df_display[col].round(2)
    
    # 显示表格
    st.dataframe(df_display, use_container_width=True)
    
    # 风控触发统计
    if '平仓原因' in df_display.columns:
        st.subheader("🛡️ 风控触发统计")
        exit_reasons = df_display['平仓原因'].value_counts()
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 饼图显示平仓原因分布
            fig = go.Figure(data=[go.Pie(
                labels=exit_reasons.index,
                values=exit_reasons.values,
                hole=0.3
            )])
            fig.update_layout(title="平仓原因分布", height=400)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            # 统计信息
            for reason, count in exit_reasons.items():
                percentage = count / len(df_display) * 100
                st.metric(f"{reason}", f"{count}次", f"{percentage:.1f}%")

def display_risk_analysis(trade_log: List[Dict], risk_config: Dict) -> None:
    """
    显示风控分析
    
    Args:
        trade_log: 交易记录
        risk_config: 风控配置
    """
    if not trade_log:
        st.info("暂无交易数据进行风控分析")
        return
    
    df_trades = pd.DataFrame(trade_log)
    
    # 风控效果分析
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 风控效果统计")
        
        # 止损触发次数
        stop_loss_count = len(df_trades[df_trades.get('exit_reason', '') == 'stop_loss'])
        take_profit_count = len(df_trades[df_trades.get('exit_reason', '') == 'take_profit'])
        signal_exit_count = len(df_trades[df_trades.get('exit_reason', '') == 'signal'])
        
        st.metric("止损触发次数", stop_loss_count)
        st.metric("止盈触发次数", take_profit_count)
        st.metric("信号平仓次数", signal_exit_count)
        
        # 风控保护效果
        if 'pnl' in df_trades.columns:
            total_loss = df_trades[df_trades['pnl'] < 0]['pnl'].sum()
            avg_loss = df_trades[df_trades['pnl'] < 0]['pnl'].mean() if len(df_trades[df_trades['pnl'] < 0]) > 0 else 0
            
            st.metric("总亏损金额", f"{total_loss:.2f} USDT")
            st.metric("平均单笔亏损", f"{avg_loss:.2f} USDT")
    
    with col2:
        st.subheader("⚙️ 风控参数效果")
        
        # 显示当前风控参数
        st.write("**当前风控设置:**")
        st.write(f"止损比例: {risk_config.get('stop_loss_pct', 'N/A')}")
        st.write(f"止盈比例: {risk_config.get('take_profit_pct', 'N/A')}")
        st.write(f"最大仓位: {risk_config.get('position_ratio', 'N/A')}")
        
        # 风控建议
        st.write("**风控建议:**")
        if stop_loss_count > len(df_trades) * 0.5:
            st.warning("止损触发频率较高，建议适当放宽止损比例")
        elif stop_loss_count < len(df_trades) * 0.1:
            st.info("止损触发较少，风控设置较为宽松")
        else:
            st.success("风控设置较为合理")

def display_comparison_analysis(metrics_no_risk: Dict, metrics_with_risk: Dict) -> None:
    """
    显示风控对比分析
    
    Args:
        metrics_no_risk: 无风控指标
        metrics_with_risk: 风控指标
    """
    st.subheader("📈 风控效果分析")
    
    # 计算改善指标
    improvements = {}
    for key in metrics_no_risk.keys():
        if key in metrics_with_risk and isinstance(metrics_no_risk[key], (int, float)):
            no_risk_val = metrics_no_risk[key]
            with_risk_val = metrics_with_risk[key]
            
            if key == "最大回撤":
                # 回撤越小越好
                improvement = (no_risk_val - with_risk_val) / abs(no_risk_val) * 100 if no_risk_val != 0 else 0
            else:
                # 其他指标越大越好
                improvement = (with_risk_val - no_risk_val) / abs(no_risk_val) * 100 if no_risk_val != 0 else 0
            
            improvements[key] = improvement
    
    # 显示改善情况
    col1, col2, col3 = st.columns(3)
    
    with col1:
        return_improvement = improvements.get("总收益率", 0)
        color = "normal" if abs(return_improvement) < 5 else ("inverse" if return_improvement < 0 else "normal")
        st.metric("收益率变化", f"{return_improvement:+.1f}%", delta_color=color)
    
    with col2:
        drawdown_improvement = improvements.get("最大回撤", 0)
        color = "normal" if drawdown_improvement > 0 else "inverse"
        st.metric("回撤改善", f"{drawdown_improvement:+.1f}%", delta_color=color)
    
    with col3:
        sharpe_improvement = improvements.get("夏普比率", 0)
        color = "normal" if sharpe_improvement > 0 else "inverse"
        st.metric("夏普比率变化", f"{sharpe_improvement:+.1f}%", delta_color=color)
    
    # 风控总结
    if return_improvement > -10 and drawdown_improvement > 0:
        st.success("🎉 风控效果良好：在控制风险的同时保持了较好的收益")
    elif drawdown_improvement > 0:
        st.info("📊 风控有效：显著降低了回撤，但对收益有一定影响")
    else:
        st.warning("⚠️ 风控参数需要调整：当前设置可能过于保守或激进")

def plot_comparison_equity_curves(df_no_risk: pd.DataFrame, df_with_risk: pd.DataFrame) -> None:
    """
    绘制风控对比净值曲线
    
    Args:
        df_no_risk: 无风控回测结果
        df_with_risk: 风控回测结果
    """
    try:
        fig = go.Figure()
        
        # 无风控净值曲线
        fig.add_trace(
            go.Scatter(
                x=df_no_risk.index,
                y=df_no_risk['equity_curve'],
                mode='lines',
                name='无风控',
                line=dict(color='blue', width=2)
            )
        )
        
        # 风控净值曲线
        fig.add_trace(
            go.Scatter(
                x=df_with_risk.index,
                y=df_with_risk['equity'],
                mode='lines',
                name='风控版本',
                line=dict(color='red', width=2)
            )
        )
        
        # 添加基准线
        fig.add_hline(y=1.0, line_dash="dash", line_color="gray", annotation_text="基准线")
        
        # 更新布局
        fig.update_layout(
            title="风控效果对比 - 净值曲线",
            xaxis_title="时间",
            yaxis_title="净值",
            height=500,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制对比净值曲线失败: {e}")

def display_trade_comparison(trade_log_no_risk: List[Dict], trade_log_with_risk: List[Dict]) -> None:
    """
    显示交易记录对比
    
    Args:
        trade_log_no_risk: 无风控交易记录
        trade_log_with_risk: 风控交易记录
    """
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 无风控交易记录")
        if trade_log_no_risk:
            df_no_risk = pd.DataFrame(trade_log_no_risk)
            st.write(f"交易次数: {len(df_no_risk)}")
            st.write(f"总盈亏: {df_no_risk['pnl'].sum():.2f} USDT")
            st.dataframe(df_no_risk[['entry_time', 'exit_time', 'pnl']].head(10))
        else:
            st.info("无交易记录")
    
    with col2:
        st.markdown("### 风控交易记录")
        if trade_log_with_risk:
            df_with_risk = pd.DataFrame(trade_log_with_risk)
            st.write(f"交易次数: {len(df_with_risk)}")
            st.write(f"总盈亏: {df_with_risk['pnl'].sum():.2f} USDT")
            st.dataframe(df_with_risk[['entry_time', 'exit_time', 'pnl']].head(10))
        else:
            st.info("无交易记录")

def plot_multi_risk_equity_curves(results: Dict[str, Dict]) -> None:
    """
    绘制多风控方案净值曲线对比
    
    Args:
        results: 多风控方案结果字典
    """
    try:
        fig = go.Figure()
        
        colors = get_chart_colors(len(results))
        
        for i, (scenario_name, scenario_result) in enumerate(results.items()):
            df_result = scenario_result['df_result']
            color = colors[i]
            
            fig.add_trace(
                go.Scatter(
                    x=df_result.index,
                    y=df_result['equity'],
                    mode='lines',
                    name=scenario_name,
                    line=dict(color=color, width=2)
                )
            )
        
        # 添加基准线
        fig.add_hline(y=1.0, line_dash="dash", line_color="gray", annotation_text="基准线")
        
        # 更新布局
        fig.update_layout(
            title="多风控方案净值曲线对比",
            xaxis_title="时间",
            yaxis_title="净值",
            height=600,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
    except Exception as e:
        st.error(f"绘制多方案净值曲线失败: {e}")
