# visualization/pages/risk_backtest.py
"""
风控回测页面
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any

# 导入项目模块
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.simple_backtest import run_backtest, calculate_performance_metrics
from backtest.risk_managed_backtest import run_backtest_with_risk_management

# 导入可视化组件
from ..components import (
    display_metric_cards,
    plot_candlestick_with_signals,
    plot_equity_curve,
    create_strategy_selector,
    create_parameter_form
)
from ..components.forms import create_risk_parameter_form
from ..components.risk_charts import (
    display_trade_log_with_risk_info,
    display_risk_analysis,
    display_comparison_analysis,
    plot_comparison_equity_curves,
    display_trade_comparison,
    plot_multi_risk_equity_curves
)
from ..utils import load_strategy_configs, SessionManager

def show_risk_managed_backtest():
    """风控回测页面"""
    st.header("🛡️ 风控回测分析")

    # 加载策略配置
    configs = load_strategy_configs()
    if not configs:
        return

    # 创建两列布局
    col1, col2 = st.columns([1, 2])

    with col1:
        st.subheader("🎯 策略与风控配置")

        # 策略选择器
        selected_idx, selected_config = create_strategy_selector(configs)
        if selected_idx == -1:
            return

        # 回测参数
        backtest_params = create_parameter_form(selected_config)

        # 风控参数
        risk_params = create_risk_parameter_form()

        # 对比模式选择
        st.subheader("📊 对比模式")
        comparison_mode = st.selectbox(
            "选择对比模式",
            ["仅风控回测", "风控 vs 无风控对比", "多风控参数对比"]
        )

        # 执行回测按钮
        if st.button("🚀 执行风控回测", type="primary"):
            with st.spinner("正在执行风控回测..."):
                _execute_risk_backtest(selected_config, backtest_params, risk_params, comparison_mode)

    with col2:
        st.subheader("📈 风控回测结果")

        # 显示回测结果
        result = SessionManager.get_risk_backtest_result()
        if result:
            _display_risk_backtest_results(result)
        else:
            st.info("请配置参数并点击执行风控回测按钮")

def _execute_risk_backtest(config: Dict, backtest_params: Dict, risk_params: Dict, comparison_mode: str) -> None:
    """
    执行风控回测

    Args:
        config: 策略配置
        backtest_params: 回测参数
        risk_params: 风控参数
        comparison_mode: 对比模式
    """
    try:
        # 更新配置参数
        config = config.copy()
        config.update(backtest_params)

        # 创建策略
        strategy = StrategyFactory.create(config)
        strategy.validate_config()

        # 获取数据
        symbol = config["symbol"]
        timeframe = config["timeframe"]
        limit = backtest_params['limit']

        df = fetch_ohlcv(symbol, timeframe, limit=limit)

        # 生成信号
        signals = strategy.generate_signals(df)

        # 构建风控配置
        risk_config = _build_risk_config(backtest_params, risk_params)

        # 根据对比模式执行不同的回测
        if comparison_mode == "仅风控回测":
            result = _run_single_risk_backtest(df, signals, risk_config, config)
        elif comparison_mode == "风控 vs 无风控对比":
            result = _run_comparison_backtest(df, signals, risk_config, backtest_params, config)
        elif comparison_mode == "多风控参数对比":
            result = _run_multi_risk_backtest(df, signals, backtest_params, risk_params, config)
        else:
            raise ValueError(f"未知的对比模式: {comparison_mode}")

        # 保存结果
        SessionManager.set_risk_backtest_result(result)

        st.success("风控回测执行完成！")

    except Exception as e:
        st.error(f"风控回测执行失败: {e}")
        # 添加详细的错误信息
        import traceback
        st.error("详细错误信息:")
        st.code(traceback.format_exc())

def _build_risk_config(backtest_params: Dict, risk_params: Dict) -> Dict:
    """
    构建风控配置

    Args:
        backtest_params: 回测参数
        risk_params: 风控参数

    Returns:
        风控配置字典
    """
    risk_config = {
        "initial_capital": backtest_params['initial_capital'],
        "position_ratio": risk_params.get('max_position_size', 1.0),
        "max_positions": 1,
        "position_sizing_method": "fixed_ratio"
    }

    # 止损止盈配置
    if risk_params.get('stop_loss_enabled', False):
        risk_config["stop_loss_pct"] = risk_params.get('stop_loss_pct', 0.05)

    if risk_params.get('take_profit_enabled', False):
        risk_config["take_profit_pct"] = risk_params.get('take_profit_pct', 0.10)

    # 最大回撤配置
    if risk_params.get('max_drawdown_enabled', False):
        risk_config["max_drawdown_pct"] = risk_params.get('max_drawdown_pct', 0.20)

    return risk_config

def _run_single_risk_backtest(df: pd.DataFrame, signals: pd.DataFrame,
                             risk_config: Dict, strategy_config: Dict) -> Dict:
    """
    执行单个风控回测

    Args:
        df: 行情数据
        signals: 策略信号
        risk_config: 风控配置
        strategy_config: 策略配置

    Returns:
        回测结果字典
    """
    # 执行风控回测
    df_result, trade_log = run_backtest_with_risk_management(
        df, signals, risk_config,
        slippage=strategy_config.get('slippage', 0.0005),
        fee=strategy_config.get('fee', 0.001)
    )

    # 计算性能指标
    metrics = calculate_performance_metrics(trade_log)

    return {
        'mode': 'single_risk',
        'df_result': df_result,
        'trade_log': trade_log,
        'metrics': metrics,
        'risk_config': risk_config,
        'strategy_config': strategy_config,
        'signals': signals
    }

def _run_comparison_backtest(df: pd.DataFrame, signals: pd.DataFrame,
                           risk_config: Dict, backtest_params: Dict, strategy_config: Dict) -> Dict:
    """
    执行风控 vs 无风控对比回测

    Args:
        df: 行情数据
        signals: 策略信号
        risk_config: 风控配置
        backtest_params: 回测参数
        strategy_config: 策略配置

    Returns:
        对比回测结果字典
    """
    # 执行无风控回测
    df_no_risk, trade_log_no_risk = run_backtest(
        df, signals,
        slippage=backtest_params['slippage'],
        fee=backtest_params['fee'],
        initial_capital=backtest_params['initial_capital']
    )
    metrics_no_risk = calculate_performance_metrics(trade_log_no_risk)

    # 执行风控回测
    df_with_risk, trade_log_with_risk = run_backtest_with_risk_management(
        df, signals, risk_config,
        slippage=backtest_params['slippage'],
        fee=backtest_params['fee']
    )
    metrics_with_risk = calculate_performance_metrics(trade_log_with_risk)

    return {
        'mode': 'comparison',
        'no_risk': {
            'df_result': df_no_risk,
            'trade_log': trade_log_no_risk,
            'metrics': metrics_no_risk
        },
        'with_risk': {
            'df_result': df_with_risk,
            'trade_log': trade_log_with_risk,
            'metrics': metrics_with_risk
        },
        'risk_config': risk_config,
        'strategy_config': strategy_config,
        'signals': signals
    }

def _run_multi_risk_backtest(df: pd.DataFrame, signals: pd.DataFrame,
                           backtest_params: Dict, risk_params: Dict, strategy_config: Dict) -> Dict:
    """
    执行多风控参数对比回测

    Args:
        df: 行情数据
        signals: 策略信号
        backtest_params: 回测参数
        risk_params: 风控参数
        strategy_config: 策略配置

    Returns:
        多风控参数对比结果字典
    """
    results = {}

    # 定义不同的风控参数组合
    risk_scenarios = [
        {"name": "保守风控", "stop_loss_pct": 0.03, "take_profit_pct": 0.06},
        {"name": "中等风控", "stop_loss_pct": 0.05, "take_profit_pct": 0.10},
        {"name": "激进风控", "stop_loss_pct": 0.08, "take_profit_pct": 0.15},
    ]

    for scenario in risk_scenarios:
        # 构建风控配置
        risk_config = _build_risk_config(backtest_params, risk_params)
        risk_config.update({
            "stop_loss_pct": scenario["stop_loss_pct"],
            "take_profit_pct": scenario["take_profit_pct"]
        })

        # 执行回测
        df_result, trade_log = run_backtest_with_risk_management(
            df, signals, risk_config,
            slippage=backtest_params['slippage'],
            fee=backtest_params['fee']
        )

        # 计算性能指标
        metrics = calculate_performance_metrics(trade_log)

        results[scenario["name"]] = {
            'df_result': df_result,
            'trade_log': trade_log,
            'metrics': metrics,
            'risk_config': risk_config
        }

    return {
        'mode': 'multi_risk',
        'results': results,
        'strategy_config': strategy_config,
        'signals': signals
    }

def _display_risk_backtest_results(result: Dict[str, Any]) -> None:
    """
    显示风控回测结果

    Args:
        result: 回测结果字典
    """
    mode = result['mode']

    if mode == 'single_risk':
        _display_single_risk_results(result)
    elif mode == 'comparison':
        _display_comparison_results(result)
    elif mode == 'multi_risk':
        _display_multi_risk_results(result)
    else:
        st.error(f"未知的结果模式: {mode}")

def _display_single_risk_results(result: Dict[str, Any]) -> None:
    """显示单个风控回测结果"""
    df_result = result['df_result']
    trade_log = result['trade_log']
    metrics = result['metrics']
    risk_config = result['risk_config']
    strategy_config = result['strategy_config']
    signals = result['signals']

    # 性能指标卡片
    display_metric_cards(metrics)

    # 风控配置信息
    with st.expander("🛡️ 风控配置详情"):
        col1, col2 = st.columns(2)

        with col1:
            st.subheader("止损止盈设置")
            stop_loss = risk_config.get('stop_loss_pct', 'N/A')
            take_profit = risk_config.get('take_profit_pct', 'N/A')
            st.write(f"止损比例: {stop_loss if stop_loss != 'N/A' else 'N/A'}")
            st.write(f"止盈比例: {take_profit if take_profit != 'N/A' else 'N/A'}")

        with col2:
            st.subheader("仓位管理")
            position_ratio = risk_config.get('position_ratio', 'N/A')
            max_drawdown = risk_config.get('max_drawdown_pct', 'N/A')
            st.write(f"仓位比例: {position_ratio if position_ratio != 'N/A' else 'N/A'}")
            st.write(f"最大回撤限制: {max_drawdown if max_drawdown != 'N/A' else 'N/A'}")

    # 创建图表标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📈 K线与信号", "💰 净值曲线", "📊 交易记录", "🛡️ 风控分析"])

    with tab1:
        st.subheader("K线图与交易信号")
        plot_candlestick_with_signals(df_result, signals, strategy_config)

    with tab2:
        st.subheader("净值曲线")
        plot_equity_curve(df_result)

    with tab3:
        st.subheader("交易记录")
        display_trade_log_with_risk_info(trade_log)

    with tab4:
        st.subheader("风控触发分析")
        display_risk_analysis(trade_log, risk_config)

def _display_comparison_results(result: Dict[str, Any]) -> None:
    """显示风控对比结果"""
    no_risk_data = result['no_risk']
    with_risk_data = result['with_risk']

    # 对比指标卡片
    st.subheader("📊 风控效果对比")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 无风控回测")
        display_metric_cards(no_risk_data['metrics'])

    with col2:
        st.markdown("### 风控回测")
        display_metric_cards(with_risk_data['metrics'])

    # 对比分析
    display_comparison_analysis(no_risk_data['metrics'], with_risk_data['metrics'])

    # 净值曲线对比
    st.subheader("📈 净值曲线对比")
    plot_comparison_equity_curves(no_risk_data['df_result'], with_risk_data['df_result'])

    # 交易记录对比
    st.subheader("📋 交易记录对比")
    display_trade_comparison(no_risk_data['trade_log'], with_risk_data['trade_log'])

def _display_multi_risk_results(result: Dict[str, Any]) -> None:
    """显示多风控参数对比结果"""
    results = result['results']

    st.subheader("📊 多风控参数对比")

    # 创建性能对比表格
    comparison_data = []
    for scenario_name, scenario_result in results.items():
        metrics = scenario_result['metrics']
        risk_config = scenario_result['risk_config']

        comparison_data.append({
            '风控方案': scenario_name,
            '总收益率': f"{metrics.get('总收益率', 0)*100:.2f}%",
            '最大回撤': f"{metrics.get('最大回撤', 0)*100:.2f}%",
            '夏普比率': f"{metrics.get('夏普比率', 0):.2f}",
            '胜率': f"{metrics.get('胜率', 0)*100:.1f}%",
            '交易次数': metrics.get('交易次数', 0),
            '止损比例': f"{risk_config.get('stop_loss_pct', 0)*100:.1f}%",
            '止盈比例': f"{risk_config.get('take_profit_pct', 0)*100:.1f}%"
        })

    comparison_df = pd.DataFrame(comparison_data)
    st.dataframe(comparison_df, use_container_width=True)

    # 净值曲线对比
    st.subheader("📈 多方案净值曲线对比")
    plot_multi_risk_equity_curves(results)
