import matplotlib.pyplot as plt
import matplotlib

matplotlib.rcParams['font.family'] = 'Microsoft YaHei'  # 支持中文标题

def plot_equity_curve_with_signals(df_result):
    import matplotlib.pyplot as plt
    import matplotlib
    matplotlib.rcParams['font.family'] = 'Microsoft YaHei'

    plt.figure(figsize=(16, 6))
    plt.plot(df_result.index, df_result["equity_curve"], label="净值曲线", color="blue")

    # 使用与模拟执行相同的逻辑来标记交易信号
    position = 0
    entry_time = None
    entry_price = None

    # 检查策略类型
    has_negative_position = (df_result["position"] < 0).any()

    for time, row in df_result.iterrows():
        equity = row["equity_curve"]
        price = row["close"]
        current_position = row["position"]

        if current_position == 1 and position == 0:
            # 开仓
            entry_time = time
            entry_price = price
            position = 1
            plt.scatter(time, equity, marker="^", color="green", label="买入信号" if '买入信号' not in plt.gca().get_legend_handles_labels()[1] else "")
        # 对于布林带突破等策略，position可以是-1（表示卖出信号）
        elif has_negative_position and current_position == -1 and position == 1:
            # 平仓
            exit_price = price
            pnl = exit_price - entry_price
            plt.scatter(time, equity, marker="v", color="red", label="卖出信号" if '卖出信号' not in plt.gca().get_legend_handles_labels()[1] else "")
            plt.text(time, equity, f"{pnl:.0f}", fontsize=8, ha="left", va="bottom", color="black")
            position = 0
        # 对于双均线等策略，position只有0和1，需要检测从1变为0的情况作为卖出信号
        elif not has_negative_position and current_position == 0 and position == 1:
            # 平仓
            exit_price = price
            pnl = exit_price - entry_price
            plt.scatter(time, equity, marker="v", color="red", label="卖出信号" if '卖出信号' not in plt.gca().get_legend_handles_labels()[1] else "")
            plt.text(time, equity, f"{pnl:.0f}", fontsize=8, ha="left", va="bottom", color="black")
            position = 0

    plt.title("策略净值曲线 + 信号点")
    plt.xlabel("时间")
    plt.ylabel("净值")
    plt.grid(True, linestyle="--", alpha=0.5)
    plt.legend()
    plt.tight_layout()
    plt.show()
