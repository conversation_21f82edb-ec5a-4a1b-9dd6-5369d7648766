# execution/risk_managed_executor.py

import csv
from pathlib import Path
import pandas as pd
from typing import Dict, Optional
from risk_management.risk_manager import ComprehensiveRiskManager
import logging

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

def simulate_execution_with_risk_management(df, risk_config=None, log_path="trade_log.csv"):
    """
    带风控的模拟交易执行

    :param df: 包含交易信号的DataFrame
    :param risk_config: 风控配置字典，如果为None则使用默认配置
    :param log_path: 交易日志保存路径
    :return: 交易日志列表
    """
    # 默认风控配置
    if risk_config is None:
        risk_config = {
            "initial_capital": 10000.0,
            "position_ratio": 1.0,
            "max_positions": 1,
            "stop_loss_pct": 0.05,
            "take_profit_pct": 0.1,
            "trailing_stop_pct": 0.03,
            "max_drawdown_pct": 0.2,
            "position_sizing_method": "fixed_ratio"
        }

    # 创建风控管理器
    risk_manager = ComprehensiveRiskManager(risk_config)

    position = 0
    entry_price = 0
    entry_time = None
    trade_log = []

    # 检查策略类型
    has_negative_position = (df["position"] < 0).any()

    # 记录初始资金
    initial_capital = risk_manager.initial_capital
    current_capital = initial_capital

    logging.info(f"初始资金: {initial_capital:.2f} USDT")
    logging.info(f"风控设置: 止损={risk_config.get('stop_loss_pct', 0.05):.2%}, "
          f"止盈={risk_config.get('take_profit_pct', 0.1):.2%}, "
          f"跟踪止损={risk_config.get('trailing_stop_pct', 0.03):.2%}")

    # 遍历每个时间点 - 修复数据泄露问题，只使用历史数据
    for i, (time, row) in enumerate(df.iterrows()):
        price = row["close"]
        current_position = row["position"]

        # 策略信号变化日志
        logging.debug(f"[{time}] 策略信号: position={current_position}, 持仓状态={position}")

        # 检查风控触发的出场信号 - 修复：只传入历史数据，与回测逻辑保持一致
        if position == 1:
            can_exit, exit_reason = risk_manager.check_exit_signal(df.iloc[:i+1], time, price, current_position, "default")
            if can_exit:
                exit_price = price
                exit_time = time
                position_size = risk_manager.get_position("default")["size"]
                pnl_pct = (exit_price - entry_price) / entry_price
                pnl = position_size * pnl_pct
                current_capital += pnl
                trade_log.append({
                    "entry_time": entry_time,
                    "entry_price": entry_price,
                    "exit_time": exit_time,
                    "exit_price": exit_price,
                    "pnl": pnl,
                    "exit_reason": exit_reason,
                    "capital": current_capital
                })
                logging.warning(f"[{exit_time}] ⚠️ 风控触发平仓: 价格={exit_price:.2f}, 盈亏={pnl:.2f}, 资金={current_capital:.2f}, 仓位={position_size:.2f}, 原因={exit_reason}")
                risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")
                position = 0

        # 处理策略信号 - 只在信号变化时触发买入（position=1表示信号从0变为1）
        elif row["position"] == 1 and position == 0:
            if risk_manager.check_entry_signal(df.iloc[:i+1], time, price, current_position, "default"):
                position_ratio = risk_manager.calculate_position_size(df.iloc[:i+1], time, price, "default")
                position_size = current_capital * position_ratio
                entry_price = price
                entry_time = time
                position = 1
                risk_manager.add_position("default", position_size, entry_price, entry_time)
                logging.info(f"[{time}] 🟢 开仓: 价格={price:.2f}, 资金={current_capital:.2f}, 仓位={position_size:.2f}, 信号={current_position}")

        # 处理策略卖出信号
        elif (has_negative_position and current_position == -1 and position == 1) or \
             (not has_negative_position and current_position == 0 and position == 1):
            exit_price = price
            exit_time = time
            position_size = risk_manager.get_position("default")["size"]
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct
            current_capital += pnl
            exit_reason = "strategy_signal"
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "exit_reason": exit_reason,
                "capital": current_capital
            })
            logging.info(f"[{exit_time}] 🔴 平仓: 价格={exit_price:.2f}, 盈亏={pnl:.2f}, 资金={current_capital:.2f}, 仓位={position_size:.2f}, 原因={exit_reason}")
            risk_manager.on_trade_completed(entry_time, entry_price, exit_time, exit_price, pnl, "default")
            position = 0

    # 处理未平仓的持仓
    if position == 1:
        # 获取最后一个价格
        final_price = df.iloc[-1]["close"]

        # 计算未实现盈亏
        position_info = risk_manager.get_position("default")
        position_size = position_info["size"]
        entry_price = position_info["entry_price"]
        unrealized_pnl = position_size * (final_price - entry_price) / entry_price

        # 更新最终资金（包含未实现盈亏）
        final_capital = current_capital + unrealized_pnl

        logging.warning(f"⚠️ 持仓未平仓，模拟盘结束时仍持有头寸")
        logging.info(f"   持仓信息: 入场价={entry_price:.2f}, 当前价={final_price:.2f}, 仓位={position_size:.2f}")
        logging.info(f"   未实现盈亏: {unrealized_pnl:.2f} USDT ({(unrealized_pnl/position_size)*100:.2f}%)")
    else:
        final_capital = current_capital

    # 计算最终收益率
    final_return = (final_capital / initial_capital - 1) * 100
    logging.info(f"\n📊 最终资金: {final_capital:.2f} USDT，收益率: {final_return:.2f}%")

    # 写入CSV文件
    if trade_log:
        log_file = Path(log_path)
        with log_file.open("w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=trade_log[0].keys())
            writer.writeheader()
            writer.writerows(trade_log)
        logging.info(f"\n✅ 交易日志保存至：{log_path}")

    return trade_log
