# backtest/simple_backtest.py

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple

def run_backtest(df: pd.DataFrame, signals: pd.DataFrame, slippage=0.0005, fee=0.001, initial_capital=10000.0) -> <PERSON><PERSON>[pd.DataFrame, List[Dict]]:
    """
    执行回测并生成交易日志

    :param df: 原始行情数据
    :param signals: 策略信号数据
    :param slippage: 滑点
    :param fee: 手续费
    :param initial_capital: 初始资金
    :return: 回测结果DataFrame和交易记录列表
    """
    df = df.copy()
    df["position"] = signals["signal"].shift(1).fillna(0)
    df["price_change"] = df["close"].pct_change().fillna(0)

    # 盈亏计算（考虑手续费和滑点）
    trade_fee = abs(df["position"].diff().fillna(0)) * fee
    trade_slip = abs(df["position"].diff().fillna(0)) * slippage
    df["strategy_return"] = df["position"] * df["price_change"] - trade_fee - trade_slip
    df["equity"] = (1 + df["strategy_return"]).cumprod()
    df["equity_curve"] = df["equity"]  # 兼容旧版本使用的列名

    df["trade"] = df["position"].diff().fillna(0)
    df["pnl"] = df["strategy_return"]

    # 生成交易日志
    trade_log = generate_trade_log(df, initial_capital)

    return df, trade_log

def generate_trade_log(df: pd.DataFrame, initial_capital=10000.0) -> List[Dict]:
    """
    根据回测结果生成交易日志

    :param df: 回测结果DataFrame
    :param initial_capital: 初始资金
    :return: 交易记录列表
    """
    trade_log = []
    position = 0
    entry_price = 0
    entry_time = None
    current_capital = initial_capital

    # 检查策略类型
    has_negative_position = (df["position"] < 0).any()

    for time, row in df.iterrows():
        # 对于布林带突破等策略，position可以是-1（表示卖出信号）
        if row["position"] == 1 and position == 0:
            # 开仓
            entry_price = row["close"]
            entry_time = time
            position = 1

        # 对于双均线等策略，position只有0和1，需要检测从1变为0的情况作为卖出信号
        elif has_negative_position and row["position"] == -1 and position == 1:
            # 布林带策略的平仓信号
            exit_price = row["close"]
            exit_time = time

            # 计算盈亏（按照固定比例仓位计算）
            position_size = current_capital * 1.0  # 假设使用100%资金
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            # 记录交易
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "position_size": position_size,
                "capital": current_capital
            })

            position = 0

        elif not has_negative_position and row["position"] == 0 and position == 1:
            # 双均线策略的平仓信号
            exit_price = row["close"]
            exit_time = time

            # 计算盈亏（按照固定比例仓位计算）
            position_size = current_capital * 1.0  # 假设使用100%资金
            pnl_pct = (exit_price - entry_price) / entry_price
            pnl = position_size * pnl_pct

            # 更新资金
            current_capital += pnl

            # 记录交易
            trade_log.append({
                "entry_time": entry_time,
                "entry_price": entry_price,
                "exit_time": exit_time,
                "exit_price": exit_price,
                "pnl": pnl,
                "position_size": position_size,
                "capital": current_capital
            })

            position = 0

    return trade_log

def calculate_performance_metrics(trade_log: List[Dict]) -> Dict:
    """
    计算策略性能指标

    :param trade_log: 交易记录列表
    :return: 性能指标字典
    """
    if not trade_log:
        return {}

    # 计算总收益率
    initial_capital = 10000.0  # 假设初始资金为10000
    final_capital = trade_log[-1]["capital"]
    total_return = (final_capital / initial_capital) - 1

    # 计算胜率
    win_trades = [t for t in trade_log if t["pnl"] > 0]
    win_rate = len(win_trades) / len(trade_log) if trade_log else 0

    # 计算平均盈亏比
    if win_trades and len(trade_log) > len(win_trades):
        avg_win = sum(t["pnl"] for t in win_trades) / len(win_trades)
        avg_loss = sum(t["pnl"] for t in trade_log if t["pnl"] <= 0) / (len(trade_log) - len(win_trades))
        profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else float('inf')
    else:
        profit_loss_ratio = 0

    # 计算最大回撤
    capitals = [initial_capital] + [t["capital"] for t in trade_log]
    max_drawdown = 0
    peak = capitals[0]

    for capital in capitals:
        if capital > peak:
            peak = capital
        drawdown = (peak - capital) / peak
        max_drawdown = max(max_drawdown, drawdown)

    return {
        "总收益率": total_return,
        "交易次数": len(trade_log),
        "胜率": win_rate,
        "平均盈亏比": profit_loss_ratio,
        "最大回撤": max_drawdown
    }
