import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
import os
from datetime import datetime, timedelta

# 导入项目模块
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.simple_backtest import run_backtest, calculate_performance_metrics
from backtest.multi_strategy_backtest import MultiStrategyBacktest
from backtest.risk_managed_backtest import run_backtest_with_risk_management
from risk_management.risk_manager import ComprehensiveRiskManager

# 设置页面配置
st.set_page_config(
    page_title="CTA策略交易系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-metric {
        border-left-color: #28a745;
    }
    .warning-metric {
        border-left-color: #ffc107;
    }
    .danger-metric {
        border-left-color: #dc3545;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主应用函数"""
    # 主标题
    st.markdown('<h1 class="main-header">📈 CTA策略交易系统</h1>', unsafe_allow_html=True)

    # 侧边栏导航
    st.sidebar.title("🎛️ 控制面板")

    # 页面选择
    page = st.sidebar.selectbox(
        "选择功能模块",
        ["📊 策略回测", "📈 多策略比较", "🛡️ 风控回测", "📋 交易记录", "⚙️ 参数配置"]
    )

    # 根据选择的页面显示对应内容
    if page == "📊 策略回测":
        show_single_strategy_backtest()
    elif page == "📈 多策略比较":
        show_multi_strategy_comparison()
    elif page == "🛡️ 风控回测":
        show_risk_managed_backtest()
    elif page == "📋 交易记录":
        show_trade_records()
    elif page == "⚙️ 参数配置":
        show_parameter_config()

def load_strategy_configs():
    """加载所有策略配置"""
    configs = []
    config_dir = "config/strategies"

    if os.path.exists(config_dir):
        for filename in os.listdir(config_dir):
            if filename.endswith('.json'):
                try:
                    with open(os.path.join(config_dir, filename), 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        config['config_file'] = filename
                        configs.append(config)
                except Exception as e:
                    st.error(f"加载配置文件 {filename} 失败: {e}")

    return configs

def show_single_strategy_backtest():
    """单策略回测页面"""
    st.header("📊 单策略回测分析")

    # 策略选择
    configs = load_strategy_configs()
    if not configs:
        st.error("未找到策略配置文件，请检查 config/strategies 目录")
        return

    # 创建两列布局
    col1, col2 = st.columns([1, 2])

    with col1:
        st.subheader("🎯 策略配置")

        # 策略选择
        strategy_names = [f"{config.get('display_name', config.get('strategy_name', 'Unknown'))} ({config['config_file']})"
                         for config in configs]
        selected_idx = st.selectbox("选择策略", range(len(strategy_names)),
                                   format_func=lambda x: strategy_names[x])

        selected_config = configs[selected_idx]

        # 显示策略信息
        st.info(f"**策略类型**: {selected_config.get('strategy_name', 'Unknown')}")
        st.info(f"**交易对**: {selected_config.get('symbol', 'Unknown')}")
        st.info(f"**时间周期**: {selected_config.get('timeframe', 'Unknown')}")

        # 回测参数
        st.subheader("⚙️ 回测参数")
        limit = st.slider("数据条数", 100, 2000, selected_config.get('limit', 500), 50)
        initial_capital = st.number_input("初始资金", 1000, 100000, 10000, 1000)
        fee = st.slider("手续费率", 0.0, 0.01, 0.001, 0.0001, format="%.4f")
        slippage = st.slider("滑点", 0.0, 0.01, 0.0005, 0.0001, format="%.4f")

        # 执行回测按钮
        if st.button("🚀 执行回测", type="primary"):
            with st.spinner("正在执行回测..."):
                execute_single_backtest(selected_config, limit, initial_capital, fee, slippage)

    with col2:
        st.subheader("📈 回测结果")

        # 检查是否有回测结果
        if 'backtest_result' in st.session_state:
            display_backtest_results(st.session_state.backtest_result)
        else:
            st.info("请选择策略并点击执行回测按钮")

def execute_single_backtest(config, limit, initial_capital, fee, slippage):
    """执行单策略回测"""
    try:
        # 更新配置参数
        config = config.copy()
        config['limit'] = limit

        # 创建策略
        strategy = StrategyFactory.create(config)
        strategy.validate_config()

        # 获取数据
        symbol = config["symbol"]
        timeframe = config["timeframe"]

        df = fetch_ohlcv(symbol, timeframe, limit=limit)

        # 生成信号
        signals = strategy.generate_signals(df)

        # 执行回测
        df_result, trade_log = run_backtest(df, signals, slippage=slippage, fee=fee, initial_capital=initial_capital)

        # 计算性能指标
        metrics = calculate_performance_metrics(trade_log)

        # 保存结果到session state
        st.session_state.backtest_result = {
            'df_result': df_result,
            'trade_log': trade_log,
            'metrics': metrics,
            'config': config,
            'signals': signals
        }

        st.success("回测执行完成！")

    except Exception as e:
        st.error(f"回测执行失败: {e}")

def show_multi_strategy_comparison():
    """多策略比较页面"""
    st.header("📈 多策略比较分析")

    # 策略选择
    configs = load_strategy_configs()
    if not configs:
        st.error("未找到策略配置文件，请检查 config/strategies 目录")
        return

    # 创建两列布局
    col1, col2 = st.columns([1, 2])

    with col1:
        st.subheader("🎯 策略选择")

        # 多选策略
        strategy_options = {}
        for i, config in enumerate(configs):
            display_name = config.get('display_name', config.get('strategy_name', 'Unknown'))
            key = f"{display_name} ({config['config_file']})"
            strategy_options[key] = i

        selected_strategies = st.multiselect(
            "选择要比较的策略（可多选）",
            options=list(strategy_options.keys()),
            default=list(strategy_options.keys())[:3] if len(strategy_options) >= 3 else list(strategy_options.keys())
        )

        if not selected_strategies:
            st.warning("请至少选择一个策略")
            return

        # 回测参数
        st.subheader("⚙️ 回测参数")
        limit = st.slider("数据条数", 100, 2000, 500, 50)
        initial_capital = st.number_input("初始资金", 1000, 100000, 10000, 1000)
        fee = st.slider("手续费率", 0.0, 0.01, 0.001, 0.0001, format="%.4f")
        slippage = st.slider("滑点", 0.0, 0.01, 0.0005, 0.0001, format="%.4f")

        # 执行比较按钮
        if st.button("🚀 执行多策略比较", type="primary"):
            selected_configs = [configs[strategy_options[name]] for name in selected_strategies]
            with st.spinner("正在执行多策略比较..."):
                execute_multi_strategy_comparison(selected_configs, limit, initial_capital, fee, slippage)

    with col2:
        st.subheader("📊 比较结果")

        # 检查是否有比较结果
        if 'multi_strategy_result' in st.session_state:
            display_multi_strategy_results(st.session_state.multi_strategy_result)
        else:
            st.info("请选择策略并点击执行比较按钮")

def execute_multi_strategy_comparison(configs, limit, initial_capital, fee, slippage):
    """执行多策略比较"""
    try:
        # 获取第一个配置中的交易对和时间周期（假设所有策略使用相同的数据）
        symbol = configs[0]["symbol"]
        timeframe = configs[0]["timeframe"]

        # 获取数据
        df = fetch_ohlcv(symbol, timeframe, limit=limit)

        # 创建多策略回测对象
        backtest = MultiStrategyBacktest(df)

        # 添加所有策略
        for config in configs:
            # 更新配置参数
            config = config.copy()
            config['limit'] = limit

            strategy = StrategyFactory.create(config)
            strategy.validate_config()

            # 使用display_name作为策略名称（如果有）
            strategy_name = config.get("display_name", config.get('strategy_name', strategy.__class__.__name__))

            backtest.add_strategy(strategy, strategy_name)

        # 获取所有策略的净值曲线
        equity_curves = backtest.get_equity_curves()

        # 获取所有策略的性能指标
        performance_metrics = backtest.get_performance_comparison()

        # 保存结果到session state
        st.session_state.multi_strategy_result = {
            'equity_curves': equity_curves,
            'performance_metrics': performance_metrics,
            'backtest_results': backtest.results,
            'configs': configs
        }

        st.success("多策略比较执行完成！")

    except Exception as e:
        st.error(f"多策略比较执行失败: {e}")

def display_multi_strategy_results(result):
    """显示多策略比较结果"""
    equity_curves = result['equity_curves']
    performance_metrics = result['performance_metrics']
    backtest_results = result['backtest_results']
    configs = result['configs']

    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📈 净值曲线对比", "📊 性能指标对比", "📉 回撤分析", "🏆 策略排名"])

    with tab1:
        st.subheader("策略净值曲线对比")
        plot_multi_equity_curves(equity_curves)

    with tab2:
        st.subheader("策略性能指标对比")
        display_performance_comparison(performance_metrics)

    with tab3:
        st.subheader("策略回撤分析")
        plot_drawdown_comparison(backtest_results)

    with tab4:
        st.subheader("策略综合排名")
        display_strategy_ranking(performance_metrics)

def plot_multi_equity_curves(equity_curves):
    """绘制多策略净值曲线对比"""
    try:
        fig = go.Figure()

        # 为每个策略添加净值曲线
        colors = px.colors.qualitative.Set1
        for i, strategy_name in enumerate(equity_curves.columns):
            color = colors[i % len(colors)]
            fig.add_trace(
                go.Scatter(
                    x=equity_curves.index,
                    y=equity_curves[strategy_name],
                    mode='lines',
                    name=strategy_name,
                    line=dict(color=color, width=2)
                )
            )

        # 添加基准线
        fig.add_hline(y=1.0, line_dash="dash", line_color="gray", annotation_text="基准线")

        # 更新布局
        fig.update_layout(
            title="多策略净值曲线对比",
            xaxis_title="时间",
            yaxis_title="净值",
            height=600,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )

        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"绘制净值曲线对比失败: {e}")

def display_performance_comparison(performance_metrics):
    """显示性能指标对比"""
    if performance_metrics.empty:
        st.info("暂无性能指标数据")
        return

    # 格式化性能指标
    formatted_metrics = performance_metrics.copy()

    # 百分比格式化
    percentage_columns = ["总收益率", "年化收益率", "最大回撤", "胜率"]
    for col in percentage_columns:
        if col in formatted_metrics.columns:
            formatted_metrics[col] = formatted_metrics[col].apply(lambda x: f"{x*100:.2f}%")

    # 数值格式化
    numeric_columns = ["夏普比率", "平均盈亏比"]
    for col in numeric_columns:
        if col in formatted_metrics.columns:
            formatted_metrics[col] = formatted_metrics[col].apply(lambda x: f"{x:.2f}")

    # 显示表格
    st.dataframe(formatted_metrics, use_container_width=True)

    # 关键指标对比图
    if not performance_metrics.empty:
        metrics_to_plot = ["总收益率", "最大回撤", "夏普比率", "胜率"]

        for metric in metrics_to_plot:
            if metric in performance_metrics.columns:
                fig = go.Figure()

                values = performance_metrics[metric]
                if metric in ["总收益率", "最大回撤", "胜率"]:
                    values = values * 100  # 转换为百分比

                colors = ['green' if v > 0 else 'red' for v in values] if metric == "总收益率" else 'blue'

                fig.add_trace(
                    go.Bar(
                        x=performance_metrics.index,
                        y=values,
                        name=metric,
                        marker_color=colors
                    )
                )

                y_title = f"{metric}(%)" if metric in ["总收益率", "最大回撤", "胜率"] else metric

                fig.update_layout(
                    title=f"{metric}对比",
                    xaxis_title="策略",
                    yaxis_title=y_title,
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

def plot_drawdown_comparison(backtest_results):
    """绘制策略回撤对比"""
    try:
        fig = go.Figure()

        colors = px.colors.qualitative.Set1
        for i, (strategy_name, result) in enumerate(backtest_results.items()):
            equity = result["equity"]
            cumulative_max = equity.cummax()
            drawdown = (equity - cumulative_max) / cumulative_max

            color = colors[i % len(colors)]
            fig.add_trace(
                go.Scatter(
                    x=result.index,
                    y=drawdown,
                    mode='lines',
                    name=f"{strategy_name} 回撤",
                    line=dict(color=color, width=2),
                    fill='tonexty' if i == 0 else None
                )
            )

        fig.update_layout(
            title="策略回撤曲线对比",
            xaxis_title="时间",
            yaxis_title="回撤",
            height=500,
            showlegend=True
        )

        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"绘制回撤对比失败: {e}")

def display_strategy_ranking(performance_metrics):
    """显示策略综合排名"""
    if performance_metrics.empty:
        st.info("暂无性能指标数据")
        return

    # 计算综合评分
    ranking_data = performance_metrics.copy()

    # 标准化指标（0-100分）
    score_weights = {
        "总收益率": 0.3,
        "夏普比率": 0.25,
        "胜率": 0.2,
        "最大回撤": 0.15,  # 回撤越小越好，需要反向计算
        "平均盈亏比": 0.1
    }

    ranking_data["综合评分"] = 0

    for metric, weight in score_weights.items():
        if metric in ranking_data.columns:
            values = ranking_data[metric]

            if metric == "最大回撤":
                # 回撤越小越好，使用负值并标准化
                normalized = (1 - (values - values.min()) / (values.max() - values.min() + 1e-8)) * 100
            else:
                # 其他指标越大越好
                normalized = (values - values.min()) / (values.max() - values.min() + 1e-8) * 100

            ranking_data["综合评分"] += normalized * weight

    # 按综合评分排序
    ranking_data = ranking_data.sort_values("综合评分", ascending=False)

    # 显示排名表格
    st.subheader("🏆 策略综合排名")

    # 格式化显示
    display_ranking = ranking_data.copy()

    # 百分比格式化
    percentage_columns = ["总收益率", "年化收益率", "最大回撤", "胜率"]
    for col in percentage_columns:
        if col in display_ranking.columns:
            display_ranking[col] = display_ranking[col].apply(lambda x: f"{x*100:.2f}%")

    # 数值格式化
    numeric_columns = ["夏普比率", "平均盈亏比", "综合评分"]
    for col in numeric_columns:
        if col in display_ranking.columns:
            display_ranking[col] = display_ranking[col].apply(lambda x: f"{x:.2f}")

    # 添加排名列
    display_ranking.insert(0, "排名", range(1, len(display_ranking) + 1))

    st.dataframe(display_ranking, use_container_width=True)

    # 综合评分可视化
    fig = go.Figure()

    colors = ['gold', 'silver', '#CD7F32'] + ['lightblue'] * (len(ranking_data) - 3)
    colors = colors[:len(ranking_data)]

    fig.add_trace(
        go.Bar(
            x=ranking_data.index,
            y=ranking_data["综合评分"],
            marker_color=colors,
            name="综合评分"
        )
    )

    fig.update_layout(
        title="策略综合评分排名",
        xaxis_title="策略",
        yaxis_title="综合评分",
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

def show_risk_managed_backtest():
    st.header("🛡️ 风控回测")
    st.info("功能开发中...")

def show_trade_records():
    st.header("📋 交易记录")
    st.info("功能开发中...")

def show_parameter_config():
    st.header("⚙️ 参数配置")
    st.info("功能开发中...")

def display_backtest_results(result):
    """显示回测结果"""
    df_result = result['df_result']
    trade_log = result['trade_log']
    metrics = result['metrics']
    config = result['config']
    signals = result['signals']

    # 性能指标卡片
    if metrics:
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_return = metrics.get("总收益率", 0) * 100
            color_class = "success-metric" if total_return > 0 else "danger-metric"
            st.markdown(f"""
            <div class="metric-card {color_class}">
                <h4>总收益率</h4>
                <h2>{total_return:.2f}%</h2>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            trade_count = metrics.get("交易次数", 0)
            st.markdown(f"""
            <div class="metric-card">
                <h4>交易次数</h4>
                <h2>{trade_count}</h2>
            </div>
            """, unsafe_allow_html=True)

        with col3:
            win_rate = metrics.get("胜率", 0) * 100
            color_class = "success-metric" if win_rate > 50 else "warning-metric"
            st.markdown(f"""
            <div class="metric-card {color_class}">
                <h4>胜率</h4>
                <h2>{win_rate:.1f}%</h2>
            </div>
            """, unsafe_allow_html=True)

        with col4:
            profit_loss_ratio = metrics.get("平均盈亏比", 0)
            color_class = "success-metric" if profit_loss_ratio > 1 else "warning-metric"
            st.markdown(f"""
            <div class="metric-card {color_class}">
                <h4>盈亏比</h4>
                <h2>{profit_loss_ratio:.2f}</h2>
            </div>
            """, unsafe_allow_html=True)

    # 创建图表标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📈 K线与信号", "💰 净值曲线", "📊 交易记录", "📋 详细指标"])

    with tab1:
        st.subheader("K线图与交易信号")
        plot_candlestick_with_signals(df_result, signals, config)

    with tab2:
        st.subheader("净值曲线")
        plot_equity_curve(df_result)

    with tab3:
        st.subheader("交易记录")
        display_trade_log(trade_log)

    with tab4:
        st.subheader("详细性能指标")
        display_detailed_metrics(metrics)

def plot_candlestick_with_signals(df_result, signals, config):
    """绘制K线图和交易信号"""
    try:
        # 创建子图
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=('价格走势与交易信号', '持仓状态'),
            row_width=[0.7, 0.3]
        )

        # K线图
        fig.add_trace(
            go.Candlestick(
                x=df_result.index,
                open=df_result['open'],
                high=df_result['high'],
                low=df_result['low'],
                close=df_result['close'],
                name="K线"
            ),
            row=1, col=1
        )

        # 添加买卖信号
        buy_signals = df_result[df_result['position'].diff() == 1]
        sell_signals = df_result[df_result['position'].diff() == -1]

        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals.index,
                    y=buy_signals['close'],
                    mode='markers',
                    marker=dict(symbol='triangle-up', size=12, color='green'),
                    name='买入信号'
                ),
                row=1, col=1
            )

        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals.index,
                    y=sell_signals['close'],
                    mode='markers',
                    marker=dict(symbol='triangle-down', size=12, color='red'),
                    name='卖出信号'
                ),
                row=1, col=1
            )

        # 持仓状态
        fig.add_trace(
            go.Scatter(
                x=df_result.index,
                y=df_result['position'],
                mode='lines',
                name='持仓状态',
                line=dict(color='blue', width=2)
            ),
            row=2, col=1
        )

        # 更新布局
        fig.update_layout(
            title=f"{config.get('symbol', 'Unknown')} - {config.get('strategy_name', 'Unknown')} 策略",
            xaxis_title="时间",
            height=800,
            showlegend=True
        )

        fig.update_yaxes(title_text="价格", row=1, col=1)
        fig.update_yaxes(title_text="持仓", row=2, col=1)

        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"绘制K线图失败: {e}")

def plot_equity_curve(df_result):
    """绘制净值曲线"""
    try:
        fig = go.Figure()

        # 净值曲线
        fig.add_trace(
            go.Scatter(
                x=df_result.index,
                y=df_result['equity_curve'],
                mode='lines',
                name='净值曲线',
                line=dict(color='blue', width=2)
            )
        )

        # 添加基准线
        fig.add_hline(y=1.0, line_dash="dash", line_color="gray", annotation_text="基准线")

        # 更新布局
        fig.update_layout(
            title="策略净值曲线",
            xaxis_title="时间",
            yaxis_title="净值",
            height=500,
            showlegend=True
        )

        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"绘制净值曲线失败: {e}")

def display_trade_log(trade_log):
    """显示交易记录"""
    if not trade_log:
        st.info("暂无交易记录")
        return

    # 转换为DataFrame
    df_trades = pd.DataFrame(trade_log)

    # 格式化时间列
    if 'entry_time' in df_trades.columns:
        df_trades['entry_time'] = pd.to_datetime(df_trades['entry_time']).dt.strftime('%Y-%m-%d %H:%M')
    if 'exit_time' in df_trades.columns:
        df_trades['exit_time'] = pd.to_datetime(df_trades['exit_time']).dt.strftime('%Y-%m-%d %H:%M')

    # 重命名列
    column_mapping = {
        'entry_time': '开仓时间',
        'entry_price': '开仓价格',
        'exit_time': '平仓时间',
        'exit_price': '平仓价格',
        'pnl': '盈亏(USDT)',
        'position_size': '仓位大小',
        'capital': '账户资金'
    }

    df_display = df_trades.rename(columns=column_mapping)

    # 格式化数值列
    numeric_columns = ['开仓价格', '平仓价格', '盈亏(USDT)', '仓位大小', '账户资金']
    for col in numeric_columns:
        if col in df_display.columns:
            df_display[col] = df_display[col].round(2)

    # 显示表格
    st.dataframe(df_display, use_container_width=True)

    # 盈亏分布图
    if '盈亏(USDT)' in df_display.columns:
        fig = go.Figure()

        colors = ['green' if x > 0 else 'red' for x in df_display['盈亏(USDT)']]

        fig.add_trace(
            go.Bar(
                x=list(range(len(df_display))),
                y=df_display['盈亏(USDT)'],
                marker_color=colors,
                name='每笔交易盈亏'
            )
        )

        fig.update_layout(
            title="每笔交易盈亏分布",
            xaxis_title="交易序号",
            yaxis_title="盈亏(USDT)",
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

def display_detailed_metrics(metrics):
    """显示详细性能指标"""
    if not metrics:
        st.info("暂无性能指标")
        return

    # 创建两列布局
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 收益指标")
        for key, value in metrics.items():
            if key in ["总收益率", "年化收益率", "最大回撤"]:
                if isinstance(value, (int, float)):
                    st.metric(key, f"{value*100:.2f}%")
                else:
                    st.metric(key, str(value))

    with col2:
        st.subheader("📈 交易指标")
        for key, value in metrics.items():
            if key in ["交易次数", "胜率", "平均盈亏比", "夏普比率"]:
                if key == "胜率" and isinstance(value, (int, float)):
                    st.metric(key, f"{value*100:.1f}%")
                elif isinstance(value, (int, float)):
                    st.metric(key, f"{value:.2f}")
                else:
                    st.metric(key, str(value))

if __name__ == "__main__":
    main()
