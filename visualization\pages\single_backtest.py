# visualization/pages/single_backtest.py
"""
单策略回测页面
"""

import streamlit as st
import pandas as pd
from typing import Dict, Any

# 导入项目模块
from strategy.base import StrategyFactory
from data.fetch_binance_kline import fetch_ohlcv
from backtest.simple_backtest import run_backtest, calculate_performance_metrics

# 导入可视化组件
from ..components import (
    display_metric_cards, 
    display_detailed_metrics,
    plot_candlestick_with_signals,
    plot_equity_curve,
    plot_trade_pnl_distribution,
    create_strategy_selector,
    create_parameter_form
)
from ..utils import load_strategy_configs, SessionManager

def show_single_strategy_backtest():
    """单策略回测页面"""
    st.header("📊 单策略回测分析")
    
    # 加载策略配置
    configs = load_strategy_configs()
    if not configs:
        return
    
    # 创建两列布局
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.subheader("🎯 策略配置")
        
        # 策略选择器
        selected_idx, selected_config = create_strategy_selector(configs)
        if selected_idx == -1:
            return
        
        # 参数表单
        params = create_parameter_form(selected_config)
        
        # 执行回测按钮
        if st.button("🚀 执行回测", type="primary"):
            with st.spinner("正在执行回测..."):
                _execute_single_backtest(selected_config, params)
    
    with col2:
        st.subheader("📈 回测结果")
        
        # 显示回测结果
        result = SessionManager.get_backtest_result()
        if result:
            _display_backtest_results(result)
        else:
            st.info("请选择策略并点击执行回测按钮")

def _execute_single_backtest(config: Dict, params: Dict) -> None:
    """
    执行单策略回测
    
    Args:
        config: 策略配置
        params: 回测参数
    """
    try:
        # 更新配置参数
        config = config.copy()
        config.update(params)
        
        # 创建策略
        strategy = StrategyFactory.create(config)
        strategy.validate_config()
        
        # 获取数据
        symbol = config["symbol"]
        timeframe = config["timeframe"]
        limit = params['limit']
        
        df = fetch_ohlcv(symbol, timeframe, limit=limit)
        
        # 生成信号
        signals = strategy.generate_signals(df)
        
        # 执行回测
        df_result, trade_log = run_backtest(
            df, signals, 
            slippage=params['slippage'], 
            fee=params['fee'], 
            initial_capital=params['initial_capital']
        )
        
        # 计算性能指标
        metrics = calculate_performance_metrics(trade_log)
        
        # 保存结果
        result = {
            'df_result': df_result,
            'trade_log': trade_log,
            'metrics': metrics,
            'config': config,
            'signals': signals,
            'params': params
        }
        SessionManager.set_backtest_result(result)
        
        st.success("回测执行完成！")
        
    except Exception as e:
        st.error(f"回测执行失败: {e}")

def _display_backtest_results(result: Dict[str, Any]) -> None:
    """
    显示回测结果
    
    Args:
        result: 回测结果字典
    """
    df_result = result['df_result']
    trade_log = result['trade_log']
    metrics = result['metrics']
    config = result['config']
    signals = result['signals']
    
    # 性能指标卡片
    display_metric_cards(metrics)
    
    # 创建图表标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📈 K线与信号", "💰 净值曲线", "📊 交易记录", "📋 详细指标"])
    
    with tab1:
        st.subheader("K线图与交易信号")
        plot_candlestick_with_signals(df_result, signals, config)
    
    with tab2:
        st.subheader("净值曲线")
        plot_equity_curve(df_result)
    
    with tab3:
        st.subheader("交易记录")
        _display_trade_log(trade_log)
    
    with tab4:
        st.subheader("详细性能指标")
        display_detailed_metrics(metrics)

def _display_trade_log(trade_log: list) -> None:
    """
    显示交易记录
    
    Args:
        trade_log: 交易记录列表
    """
    if not trade_log:
        st.info("暂无交易记录")
        return
    
    # 转换为DataFrame
    df_trades = pd.DataFrame(trade_log)
    
    # 格式化时间列
    if 'entry_time' in df_trades.columns:
        df_trades['entry_time'] = pd.to_datetime(df_trades['entry_time']).dt.strftime('%Y-%m-%d %H:%M')
    if 'exit_time' in df_trades.columns:
        df_trades['exit_time'] = pd.to_datetime(df_trades['exit_time']).dt.strftime('%Y-%m-%d %H:%M')
    
    # 重命名列
    column_mapping = {
        'entry_time': '开仓时间',
        'entry_price': '开仓价格',
        'exit_time': '平仓时间',
        'exit_price': '平仓价格',
        'pnl': '盈亏(USDT)',
        'position_size': '仓位大小',
        'capital': '账户资金'
    }
    
    df_display = df_trades.rename(columns=column_mapping)
    
    # 格式化数值列
    numeric_columns = ['开仓价格', '平仓价格', '盈亏(USDT)', '仓位大小', '账户资金']
    for col in numeric_columns:
        if col in df_display.columns:
            df_display[col] = df_display[col].round(2)
    
    # 显示表格
    st.dataframe(df_display, use_container_width=True)
    
    # 盈亏分布图
    plot_trade_pnl_distribution(trade_log)
