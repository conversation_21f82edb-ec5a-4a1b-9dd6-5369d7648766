# cta_mvp/utils/plot_pnl.py

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib

matplotlib.rcParams['font.family'] = 'Microsoft YaHei'  # 支持中文标题

def plot_trade_pnl(csv_path="trade_log.csv"):
    try:
        df = pd.read_csv(csv_path, parse_dates=["entry_time", "exit_time"])
    except FileNotFoundError:
        print(f"❌ 找不到交易日志文件：{csv_path}")
        return

    plt.figure(figsize=(12, 5))
    df["pnl"].plot(kind="bar", color=df["pnl"].apply(lambda x: "green" if x > 0 else "red"))
    plt.title("每笔交易盈亏情况")
    plt.xlabel("交易序号")
    plt.ylabel("盈亏（USDT）")
    plt.grid(True, linestyle="--", alpha=0.6)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.show()
