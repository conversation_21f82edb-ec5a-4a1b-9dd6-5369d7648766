# visualization/components/metrics.py
"""
指标展示组件模块
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from typing import Dict, Any, List

from ..config import METRICS_CONFIG

def format_percentage(value: float, decimals: int = 2) -> str:
    """
    格式化百分比

    Args:
        value: 数值
        decimals: 小数位数

    Returns:
        格式化后的百分比字符串
    """
    return f"{value * 100:.{decimals}f}%"

def format_number(value: float, decimals: int = 2) -> str:
    """
    格式化数字

    Args:
        value: 数值
        decimals: 小数位数

    Returns:
        格式化后的数字字符串
    """
    return f"{value:.{decimals}f}"

def display_metric_cards(metrics: Dict[str, Any]) -> None:
    """
    显示性能指标卡片

    Args:
        metrics: 性能指标字典
    """
    if not metrics:
        st.info("暂无性能指标")
        return

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_return = metrics.get("总收益率", 0) * 100
        color_class = "success-metric" if total_return > 0 else "danger-metric"
        st.markdown(f"""
        <div class="metric-card {color_class}">
            <h4>总收益率</h4>
            <h2>{total_return:.2f}%</h2>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        trade_count = metrics.get("交易次数", 0)
        st.markdown(f"""
        <div class="metric-card info-metric">
            <h4>交易次数</h4>
            <h2>{trade_count}</h2>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        win_rate = metrics.get("胜率", 0) * 100
        color_class = "success-metric" if win_rate > 50 else "warning-metric"
        st.markdown(f"""
        <div class="metric-card {color_class}">
            <h4>胜率</h4>
            <h2>{win_rate:.1f}%</h2>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        profit_loss_ratio = metrics.get("平均盈亏比", 0)
        color_class = "success-metric" if profit_loss_ratio > 1 else "warning-metric"
        st.markdown(f"""
        <div class="metric-card {color_class}">
            <h4>盈亏比</h4>
            <h2>{profit_loss_ratio:.2f}</h2>
        </div>
        """, unsafe_allow_html=True)

def display_performance_table(performance_metrics: pd.DataFrame) -> None:
    """
    显示性能指标表格

    Args:
        performance_metrics: 性能指标DataFrame
    """
    if performance_metrics.empty:
        st.info("暂无性能指标数据")
        return

    # 格式化性能指标
    formatted_metrics = performance_metrics.copy()

    # 百分比格式化
    percentage_columns = METRICS_CONFIG["percentage_columns"]
    for col in percentage_columns:
        if col in formatted_metrics.columns:
            formatted_metrics[col] = formatted_metrics[col].apply(
                lambda x: format_percentage(x) if pd.notna(x) else "N/A"
            )

    # 数值格式化
    numeric_columns = METRICS_CONFIG["numeric_columns"]
    for col in numeric_columns:
        if col in formatted_metrics.columns:
            formatted_metrics[col] = formatted_metrics[col].apply(
                lambda x: format_number(x) if pd.notna(x) else "N/A"
            )

    # 显示表格
    st.dataframe(formatted_metrics, use_container_width=True)

def display_strategy_ranking(performance_metrics: pd.DataFrame) -> None:
    """
    显示策略综合排名

    Args:
        performance_metrics: 性能指标DataFrame
    """
    if performance_metrics.empty:
        st.info("暂无性能指标数据")
        return

    # 计算综合评分
    ranking_data = performance_metrics.copy()
    score_weights = METRICS_CONFIG["ranking_weights"]

    ranking_data["综合评分"] = 0

    for metric, weight in score_weights.items():
        if metric in ranking_data.columns:
            values = ranking_data[metric]

            if metric == "最大回撤":
                # 回撤越小越好，使用负值并标准化
                normalized = (1 - (values - values.min()) / (values.max() - values.min() + 1e-8)) * 100
            else:
                # 其他指标越大越好
                normalized = (values - values.min()) / (values.max() - values.min() + 1e-8) * 100

            ranking_data["综合评分"] += normalized * weight

    # 按综合评分排序
    ranking_data = ranking_data.sort_values("综合评分", ascending=False)

    # 显示排名表格
    st.subheader("🏆 策略综合排名")

    # 格式化显示
    display_ranking = ranking_data.copy()

    # 百分比格式化
    percentage_columns = METRICS_CONFIG["percentage_columns"]
    for col in percentage_columns:
        if col in display_ranking.columns:
            display_ranking[col] = display_ranking[col].apply(
                lambda x: format_percentage(x) if pd.notna(x) else "N/A"
            )

    # 数值格式化
    numeric_columns = METRICS_CONFIG["numeric_columns"]
    for col in numeric_columns:
        if col in display_ranking.columns:
            display_ranking[col] = display_ranking[col].apply(
                lambda x: format_number(x) if pd.notna(x) else "N/A"
            )

    # 添加排名列
    display_ranking.insert(0, "排名", range(1, len(display_ranking) + 1))

    st.dataframe(display_ranking, use_container_width=True)

    # 综合评分可视化
    _plot_ranking_chart(ranking_data)

def _plot_ranking_chart(ranking_data: pd.DataFrame) -> None:
    """
    绘制排名图表

    Args:
        ranking_data: 排名数据
    """
    try:
        fig = go.Figure()

        colors = ['gold', 'silver', '#CD7F32'] + ['lightblue'] * (len(ranking_data) - 3)
        colors = colors[:len(ranking_data)]

        fig.add_trace(
            go.Bar(
                x=ranking_data.index,
                y=ranking_data["综合评分"],
                marker_color=colors,
                name="综合评分"
            )
        )

        fig.update_layout(
            title="策略综合评分排名",
            xaxis_title="策略",
            yaxis_title="综合评分",
            height=400
        )

        st.plotly_chart(fig, use_container_width=True)

    except Exception as e:
        st.error(f"绘制排名图表失败: {e}")

def display_detailed_metrics(metrics: Dict[str, Any]) -> None:
    """
    显示详细性能指标

    Args:
        metrics: 性能指标字典
    """
    if not metrics:
        st.info("暂无性能指标")
        return

    # 创建两列布局
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 收益指标")
        for key, value in metrics.items():
            if key in ["总收益率", "年化收益率", "最大回撤"]:
                if isinstance(value, (int, float)):
                    st.metric(key, format_percentage(value))
                else:
                    st.metric(key, str(value))

    with col2:
        st.subheader("📈 交易指标")
        for key, value in metrics.items():
            if key in ["交易次数", "胜率", "平均盈亏比", "夏普比率"]:
                if key == "胜率" and isinstance(value, (int, float)):
                    st.metric(key, format_percentage(value, 1))
                elif isinstance(value, (int, float)):
                    st.metric(key, format_number(value))
                else:
                    st.metric(key, str(value))
